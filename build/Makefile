# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/C/cmake/bin/cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/C/cmake/bin/cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles /E/github/astrometry.net_win/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named qfits

# Build rule for target.
qfits: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qfits
.PHONY : qfits

# fast build rule for target.
qfits/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/build
.PHONY : qfits/fast

#=============================================================================
# Target rules for targets named anbase

# Build rule for target.
anbase: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 anbase
.PHONY : anbase

# fast build rule for target.
anbase/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/build
.PHONY : anbase/fast

#=============================================================================
# Target rules for targets named anutils

# Build rule for target.
anutils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 anutils
.PHONY : anutils

# fast build rule for target.
anutils/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/build
.PHONY : anutils/fast

#=============================================================================
# Target rules for targets named anfiles

# Build rule for target.
anfiles: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 anfiles
.PHONY : anfiles

# fast build rule for target.
anfiles/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/build
.PHONY : anfiles/fast

#=============================================================================
# Target rules for targets named libkd

# Build rule for target.
libkd: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libkd
.PHONY : libkd

# fast build rule for target.
libkd/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/build
.PHONY : libkd/fast

#=============================================================================
# Target rules for targets named catalogs

# Build rule for target.
catalogs: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 catalogs
.PHONY : catalogs

# fast build rule for target.
catalogs/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/build
.PHONY : catalogs/fast

#=============================================================================
# Target rules for targets named astrometry

# Build rule for target.
astrometry: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 astrometry
.PHONY : astrometry

# fast build rule for target.
astrometry/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/build
.PHONY : astrometry/fast

#=============================================================================
# Target rules for targets named solve-field

# Build rule for target.
solve-field: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 solve-field
.PHONY : solve-field

# fast build rule for target.
solve-field/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/build
.PHONY : solve-field/fast

#=============================================================================
# Target rules for targets named astrometry-engine

# Build rule for target.
astrometry-engine: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 astrometry-engine
.PHONY : astrometry-engine

# fast build rule for target.
astrometry-engine/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/build
.PHONY : astrometry-engine/fast

#=============================================================================
# Target rules for targets named test-find-executable

# Build rule for target.
test-find-executable: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test-find-executable
.PHONY : test-find-executable

# fast build rule for target.
test-find-executable/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/build
.PHONY : test-find-executable/fast

#=============================================================================
# Target rules for targets named an-fitstopnm

# Build rule for target.
an-fitstopnm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 an-fitstopnm
.PHONY : an-fitstopnm

# fast build rule for target.
an-fitstopnm/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/build
.PHONY : an-fitstopnm/fast

#=============================================================================
# Target rules for targets named an-pnmtofits

# Build rule for target.
an-pnmtofits: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 an-pnmtofits
.PHONY : an-pnmtofits

# fast build rule for target.
an-pnmtofits/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/build
.PHONY : an-pnmtofits/fast

#=============================================================================
# Target rules for targets named image2pnm

# Build rule for target.
image2pnm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 image2pnm
.PHONY : image2pnm

# fast build rule for target.
image2pnm/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/build
.PHONY : image2pnm/fast

#=============================================================================
# Target rules for targets named removelines

# Build rule for target.
removelines: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 removelines
.PHONY : removelines

# fast build rule for target.
removelines/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/build
.PHONY : removelines/fast

#=============================================================================
# Target rules for targets named uniformize

# Build rule for target.
uniformize: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uniformize
.PHONY : uniformize

# fast build rule for target.
uniformize/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/build
.PHONY : uniformize/fast

#=============================================================================
# Target rules for targets named test_fits_read

# Build rule for target.
test_fits_read: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_fits_read
.PHONY : test_fits_read

# fast build rule for target.
test_fits_read/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/build
.PHONY : test_fits_read/fast

#=============================================================================
# Target rules for targets named debug_fits

# Build rule for target.
debug_fits: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 debug_fits
.PHONY : debug_fits

# fast build rule for target.
debug_fits/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/build
.PHONY : debug_fits/fast

#=============================================================================
# Target rules for targets named debug_cfitsio

# Build rule for target.
debug_cfitsio: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 debug_cfitsio
.PHONY : debug_cfitsio

# fast build rule for target.
debug_cfitsio/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/build
.PHONY : debug_cfitsio/fast

#=============================================================================
# Target rules for targets named debug_qfits_detailed

# Build rule for target.
debug_qfits_detailed: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 debug_qfits_detailed
.PHONY : debug_qfits_detailed

# fast build rule for target.
debug_qfits_detailed/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/build
.PHONY : debug_qfits_detailed/fast

#=============================================================================
# Target rules for targets named copy_dlls

# Build rule for target.
copy_dlls: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 copy_dlls
.PHONY : copy_dlls

# fast build rule for target.
copy_dlls/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_dlls.dir/build.make CMakeFiles/copy_dlls.dir/build
.PHONY : copy_dlls/fast

E_/github/astrometry.net_win/catalogs/2mass.obj: E_/github/astrometry.net_win/catalogs/2mass.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/2mass.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/2mass.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/2mass.c.obj

E_/github/astrometry.net_win/catalogs/2mass.i: E_/github/astrometry.net_win/catalogs/2mass.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/2mass.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/2mass.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/2mass.c.i

E_/github/astrometry.net_win/catalogs/2mass.s: E_/github/astrometry.net_win/catalogs/2mass.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/2mass.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/2mass.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/2mass.c.s

E_/github/astrometry.net_win/catalogs/brightstars.obj: E_/github/astrometry.net_win/catalogs/brightstars.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/brightstars.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/brightstars.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/brightstars.c.obj

E_/github/astrometry.net_win/catalogs/brightstars.i: E_/github/astrometry.net_win/catalogs/brightstars.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/brightstars.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/brightstars.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/brightstars.c.i

E_/github/astrometry.net_win/catalogs/brightstars.s: E_/github/astrometry.net_win/catalogs/brightstars.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/brightstars.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/brightstars.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/brightstars.c.s

E_/github/astrometry.net_win/catalogs/constellation-boundaries.obj: E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/constellation-boundaries.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj

E_/github/astrometry.net_win/catalogs/constellation-boundaries.i: E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/constellation-boundaries.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i

E_/github/astrometry.net_win/catalogs/constellation-boundaries.s: E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/constellation-boundaries.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s

E_/github/astrometry.net_win/catalogs/constellations.obj: E_/github/astrometry.net_win/catalogs/constellations.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/constellations.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/constellations.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/constellations.c.obj

E_/github/astrometry.net_win/catalogs/constellations.i: E_/github/astrometry.net_win/catalogs/constellations.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/constellations.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/constellations.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/constellations.c.i

E_/github/astrometry.net_win/catalogs/constellations.s: E_/github/astrometry.net_win/catalogs/constellations.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/constellations.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/constellations.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/constellations.c.s

E_/github/astrometry.net_win/catalogs/hd.obj: E_/github/astrometry.net_win/catalogs/hd.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/hd.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/hd.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/hd.c.obj

E_/github/astrometry.net_win/catalogs/hd.i: E_/github/astrometry.net_win/catalogs/hd.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/hd.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/hd.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/hd.c.i

E_/github/astrometry.net_win/catalogs/hd.s: E_/github/astrometry.net_win/catalogs/hd.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/hd.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/hd.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/hd.c.s

E_/github/astrometry.net_win/catalogs/nomad.obj: E_/github/astrometry.net_win/catalogs/nomad.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/nomad.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/nomad.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/nomad.c.obj

E_/github/astrometry.net_win/catalogs/nomad.i: E_/github/astrometry.net_win/catalogs/nomad.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/nomad.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/nomad.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/nomad.c.i

E_/github/astrometry.net_win/catalogs/nomad.s: E_/github/astrometry.net_win/catalogs/nomad.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/nomad.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/nomad.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/nomad.c.s

E_/github/astrometry.net_win/catalogs/openngc.obj: E_/github/astrometry.net_win/catalogs/openngc.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/openngc.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/openngc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/openngc.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/openngc.c.obj

E_/github/astrometry.net_win/catalogs/openngc.i: E_/github/astrometry.net_win/catalogs/openngc.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/openngc.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/openngc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/openngc.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/openngc.c.i

E_/github/astrometry.net_win/catalogs/openngc.s: E_/github/astrometry.net_win/catalogs/openngc.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/openngc.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/openngc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/openngc.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/openngc.c.s

E_/github/astrometry.net_win/catalogs/stellarium-constellations.obj: E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/stellarium-constellations.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj

E_/github/astrometry.net_win/catalogs/stellarium-constellations.i: E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/stellarium-constellations.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i

E_/github/astrometry.net_win/catalogs/stellarium-constellations.s: E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/stellarium-constellations.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s

E_/github/astrometry.net_win/catalogs/tycho2.obj: E_/github/astrometry.net_win/catalogs/tycho2.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/tycho2.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/tycho2.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/tycho2.c.obj

E_/github/astrometry.net_win/catalogs/tycho2.i: E_/github/astrometry.net_win/catalogs/tycho2.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/tycho2.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/tycho2.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/tycho2.c.i

E_/github/astrometry.net_win/catalogs/tycho2.s: E_/github/astrometry.net_win/catalogs/tycho2.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/tycho2.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/tycho2.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/tycho2.c.s

E_/github/astrometry.net_win/catalogs/ucac3.obj: E_/github/astrometry.net_win/catalogs/ucac3.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/ucac3.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/ucac3.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/ucac3.c.obj

E_/github/astrometry.net_win/catalogs/ucac3.i: E_/github/astrometry.net_win/catalogs/ucac3.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/ucac3.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/ucac3.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/ucac3.c.i

E_/github/astrometry.net_win/catalogs/ucac3.s: E_/github/astrometry.net_win/catalogs/ucac3.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/ucac3.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/ucac3.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/ucac3.c.s

E_/github/astrometry.net_win/catalogs/ucac4.obj: E_/github/astrometry.net_win/catalogs/ucac4.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/ucac4.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/ucac4.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/ucac4.c.obj

E_/github/astrometry.net_win/catalogs/ucac4.i: E_/github/astrometry.net_win/catalogs/ucac4.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/ucac4.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/ucac4.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/ucac4.c.i

E_/github/astrometry.net_win/catalogs/ucac4.s: E_/github/astrometry.net_win/catalogs/ucac4.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/ucac4.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/ucac4.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/ucac4.c.s

E_/github/astrometry.net_win/catalogs/usnob.obj: E_/github/astrometry.net_win/catalogs/usnob.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/usnob.obj

# target to build an object file
E_/github/astrometry.net_win/catalogs/usnob.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj
.PHONY : E_/github/astrometry.net_win/catalogs/usnob.c.obj

E_/github/astrometry.net_win/catalogs/usnob.i: E_/github/astrometry.net_win/catalogs/usnob.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/usnob.i

# target to preprocess a source file
E_/github/astrometry.net_win/catalogs/usnob.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.i
.PHONY : E_/github/astrometry.net_win/catalogs/usnob.c.i

E_/github/astrometry.net_win/catalogs/usnob.s: E_/github/astrometry.net_win/catalogs/usnob.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/usnob.s

# target to generate assembly for a file
E_/github/astrometry.net_win/catalogs/usnob.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.s
.PHONY : E_/github/astrometry.net_win/catalogs/usnob.c.s

E_/github/astrometry.net_win/libkd/kdint_ddd.obj: E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddd.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj

E_/github/astrometry.net_win/libkd/kdint_ddd.i: E_/github/astrometry.net_win/libkd/kdint_ddd.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddd.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_ddd.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddd.c.i

E_/github/astrometry.net_win/libkd/kdint_ddd.s: E_/github/astrometry.net_win/libkd/kdint_ddd.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddd.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_ddd.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddd.c.s

E_/github/astrometry.net_win/libkd/kdint_dds.obj: E_/github/astrometry.net_win/libkd/kdint_dds.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dds.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_dds.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dds.c.obj

E_/github/astrometry.net_win/libkd/kdint_dds.i: E_/github/astrometry.net_win/libkd/kdint_dds.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dds.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_dds.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dds.c.i

E_/github/astrometry.net_win/libkd/kdint_dds.s: E_/github/astrometry.net_win/libkd/kdint_dds.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dds.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_dds.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dds.c.s

E_/github/astrometry.net_win/libkd/kdint_ddu.obj: E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddu.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj

E_/github/astrometry.net_win/libkd/kdint_ddu.i: E_/github/astrometry.net_win/libkd/kdint_ddu.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddu.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_ddu.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddu.c.i

E_/github/astrometry.net_win/libkd/kdint_ddu.s: E_/github/astrometry.net_win/libkd/kdint_ddu.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddu.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_ddu.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_ddu.c.s

E_/github/astrometry.net_win/libkd/kdint_dss.obj: E_/github/astrometry.net_win/libkd/kdint_dss.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dss.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_dss.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dss.c.obj

E_/github/astrometry.net_win/libkd/kdint_dss.i: E_/github/astrometry.net_win/libkd/kdint_dss.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dss.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_dss.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dss.c.i

E_/github/astrometry.net_win/libkd/kdint_dss.s: E_/github/astrometry.net_win/libkd/kdint_dss.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dss.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_dss.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_dss.c.s

E_/github/astrometry.net_win/libkd/kdint_duu.obj: E_/github/astrometry.net_win/libkd/kdint_duu.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_duu.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_duu.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_duu.c.obj

E_/github/astrometry.net_win/libkd/kdint_duu.i: E_/github/astrometry.net_win/libkd/kdint_duu.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_duu.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_duu.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_duu.c.i

E_/github/astrometry.net_win/libkd/kdint_duu.s: E_/github/astrometry.net_win/libkd/kdint_duu.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_duu.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_duu.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_duu.c.s

E_/github/astrometry.net_win/libkd/kdint_fff.obj: E_/github/astrometry.net_win/libkd/kdint_fff.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_fff.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_fff.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_fff.c.obj

E_/github/astrometry.net_win/libkd/kdint_fff.i: E_/github/astrometry.net_win/libkd/kdint_fff.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_fff.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_fff.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_fff.c.i

E_/github/astrometry.net_win/libkd/kdint_fff.s: E_/github/astrometry.net_win/libkd/kdint_fff.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_fff.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_fff.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_fff.c.s

E_/github/astrometry.net_win/libkd/kdint_lll.obj: E_/github/astrometry.net_win/libkd/kdint_lll.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_lll.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdint_lll.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdint_lll.c.obj

E_/github/astrometry.net_win/libkd/kdint_lll.i: E_/github/astrometry.net_win/libkd/kdint_lll.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_lll.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdint_lll.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdint_lll.c.i

E_/github/astrometry.net_win/libkd/kdint_lll.s: E_/github/astrometry.net_win/libkd/kdint_lll.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_lll.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdint_lll.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdint_lll.c.s

E_/github/astrometry.net_win/libkd/kdtree.obj: E_/github/astrometry.net_win/libkd/kdtree.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdtree.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdtree.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdtree.c.obj

E_/github/astrometry.net_win/libkd/kdtree.i: E_/github/astrometry.net_win/libkd/kdtree.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdtree.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdtree.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdtree.c.i

E_/github/astrometry.net_win/libkd/kdtree.s: E_/github/astrometry.net_win/libkd/kdtree.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdtree.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdtree.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdtree.c.s

E_/github/astrometry.net_win/libkd/kdtree_dim.obj: E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_dim.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj

E_/github/astrometry.net_win/libkd/kdtree_dim.i: E_/github/astrometry.net_win/libkd/kdtree_dim.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_dim.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdtree_dim.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_dim.c.i

E_/github/astrometry.net_win/libkd/kdtree_dim.s: E_/github/astrometry.net_win/libkd/kdtree_dim.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_dim.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdtree_dim.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_dim.c.s

E_/github/astrometry.net_win/libkd/kdtree_fits_io.obj: E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_fits_io.obj

# target to build an object file
E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj

E_/github/astrometry.net_win/libkd/kdtree_fits_io.i: E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_fits_io.i

# target to preprocess a source file
E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i

E_/github/astrometry.net_win/libkd/kdtree_fits_io.s: E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_fits_io.s

# target to generate assembly for a file
E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s
.PHONY : E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s

E_/github/astrometry.net_win/qfits-an/anqfits.obj: E_/github/astrometry.net_win/qfits-an/anqfits.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/anqfits.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/anqfits.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/anqfits.c.obj

E_/github/astrometry.net_win/qfits-an/anqfits.i: E_/github/astrometry.net_win/qfits-an/anqfits.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/anqfits.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/anqfits.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/anqfits.c.i

E_/github/astrometry.net_win/qfits-an/anqfits.s: E_/github/astrometry.net_win/qfits-an/anqfits.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/anqfits.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/anqfits.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/anqfits.c.s

E_/github/astrometry.net_win/qfits-an/md5.obj: E_/github/astrometry.net_win/qfits-an/md5.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/md5.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/md5.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/md5.c.obj

E_/github/astrometry.net_win/qfits-an/md5.i: E_/github/astrometry.net_win/qfits-an/md5.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/md5.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/md5.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/md5.c.i

E_/github/astrometry.net_win/qfits-an/md5.s: E_/github/astrometry.net_win/qfits-an/md5.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/md5.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/md5.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/md5.c.s

E_/github/astrometry.net_win/qfits-an/qfits_byteswap.obj: E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_byteswap.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_byteswap.i: E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_byteswap.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i

E_/github/astrometry.net_win/qfits-an/qfits_byteswap.s: E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_byteswap.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s

E_/github/astrometry.net_win/qfits-an/qfits_card.obj: E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_card.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_card.i: E_/github/astrometry.net_win/qfits-an/qfits_card.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_card.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_card.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_card.c.i

E_/github/astrometry.net_win/qfits-an/qfits_card.s: E_/github/astrometry.net_win/qfits-an/qfits_card.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_card.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_card.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_card.c.s

E_/github/astrometry.net_win/qfits-an/qfits_convert.obj: E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_convert.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_convert.i: E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_convert.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i

E_/github/astrometry.net_win/qfits-an/qfits_convert.s: E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_convert.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s

E_/github/astrometry.net_win/qfits-an/qfits_error.obj: E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_error.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_error.i: E_/github/astrometry.net_win/qfits-an/qfits_error.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_error.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_error.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_error.c.i

E_/github/astrometry.net_win/qfits-an/qfits_error.s: E_/github/astrometry.net_win/qfits-an/qfits_error.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_error.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_error.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_error.c.s

E_/github/astrometry.net_win/qfits-an/qfits_float.obj: E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_float.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_float.i: E_/github/astrometry.net_win/qfits-an/qfits_float.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_float.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_float.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_float.c.i

E_/github/astrometry.net_win/qfits-an/qfits_float.s: E_/github/astrometry.net_win/qfits-an/qfits_float.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_float.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_float.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_float.c.s

E_/github/astrometry.net_win/qfits-an/qfits_header.obj: E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_header.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_header.i: E_/github/astrometry.net_win/qfits-an/qfits_header.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_header.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_header.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_header.c.i

E_/github/astrometry.net_win/qfits-an/qfits_header.s: E_/github/astrometry.net_win/qfits-an/qfits_header.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_header.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_header.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_header.c.s

E_/github/astrometry.net_win/qfits-an/qfits_image.obj: E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_image.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_image.i: E_/github/astrometry.net_win/qfits-an/qfits_image.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_image.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_image.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_image.c.i

E_/github/astrometry.net_win/qfits-an/qfits_image.s: E_/github/astrometry.net_win/qfits-an/qfits_image.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_image.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_image.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_image.c.s

E_/github/astrometry.net_win/qfits-an/qfits_md5.obj: E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_md5.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_md5.i: E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_md5.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i

E_/github/astrometry.net_win/qfits-an/qfits_md5.s: E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_md5.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s

E_/github/astrometry.net_win/qfits-an/qfits_memory.obj: E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_memory.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_memory.i: E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_memory.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i

E_/github/astrometry.net_win/qfits-an/qfits_memory.s: E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_memory.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s

E_/github/astrometry.net_win/qfits-an/qfits_rw.obj: E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_rw.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_rw.i: E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_rw.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i

E_/github/astrometry.net_win/qfits-an/qfits_rw.s: E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_rw.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s

E_/github/astrometry.net_win/qfits-an/qfits_table.obj: E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_table.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_table.i: E_/github/astrometry.net_win/qfits-an/qfits_table.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_table.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_table.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_table.c.i

E_/github/astrometry.net_win/qfits-an/qfits_table.s: E_/github/astrometry.net_win/qfits-an/qfits_table.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_table.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_table.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_table.c.s

E_/github/astrometry.net_win/qfits-an/qfits_time.obj: E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_time.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_time.i: E_/github/astrometry.net_win/qfits-an/qfits_time.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_time.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_time.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_time.c.i

E_/github/astrometry.net_win/qfits-an/qfits_time.s: E_/github/astrometry.net_win/qfits-an/qfits_time.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_time.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_time.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_time.c.s

E_/github/astrometry.net_win/qfits-an/qfits_tools.obj: E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_tools.obj

# target to build an object file
E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj

E_/github/astrometry.net_win/qfits-an/qfits_tools.i: E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_tools.i

# target to preprocess a source file
E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i

E_/github/astrometry.net_win/qfits-an/qfits_tools.s: E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_tools.s

# target to generate assembly for a file
E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s
.PHONY : E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s

E_/github/astrometry.net_win/solver/augment-xylist.obj: E_/github/astrometry.net_win/solver/augment-xylist.c.obj
.PHONY : E_/github/astrometry.net_win/solver/augment-xylist.obj

# target to build an object file
E_/github/astrometry.net_win/solver/augment-xylist.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj
.PHONY : E_/github/astrometry.net_win/solver/augment-xylist.c.obj

E_/github/astrometry.net_win/solver/augment-xylist.i: E_/github/astrometry.net_win/solver/augment-xylist.c.i
.PHONY : E_/github/astrometry.net_win/solver/augment-xylist.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/augment-xylist.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.i
.PHONY : E_/github/astrometry.net_win/solver/augment-xylist.c.i

E_/github/astrometry.net_win/solver/augment-xylist.s: E_/github/astrometry.net_win/solver/augment-xylist.c.s
.PHONY : E_/github/astrometry.net_win/solver/augment-xylist.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/augment-xylist.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.s
.PHONY : E_/github/astrometry.net_win/solver/augment-xylist.c.s

E_/github/astrometry.net_win/solver/build-index.obj: E_/github/astrometry.net_win/solver/build-index.c.obj
.PHONY : E_/github/astrometry.net_win/solver/build-index.obj

# target to build an object file
E_/github/astrometry.net_win/solver/build-index.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj
.PHONY : E_/github/astrometry.net_win/solver/build-index.c.obj

E_/github/astrometry.net_win/solver/build-index.i: E_/github/astrometry.net_win/solver/build-index.c.i
.PHONY : E_/github/astrometry.net_win/solver/build-index.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/build-index.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.i
.PHONY : E_/github/astrometry.net_win/solver/build-index.c.i

E_/github/astrometry.net_win/solver/build-index.s: E_/github/astrometry.net_win/solver/build-index.c.s
.PHONY : E_/github/astrometry.net_win/solver/build-index.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/build-index.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.s
.PHONY : E_/github/astrometry.net_win/solver/build-index.c.s

E_/github/astrometry.net_win/solver/codefile.obj: E_/github/astrometry.net_win/solver/codefile.c.obj
.PHONY : E_/github/astrometry.net_win/solver/codefile.obj

# target to build an object file
E_/github/astrometry.net_win/solver/codefile.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj
.PHONY : E_/github/astrometry.net_win/solver/codefile.c.obj

E_/github/astrometry.net_win/solver/codefile.i: E_/github/astrometry.net_win/solver/codefile.c.i
.PHONY : E_/github/astrometry.net_win/solver/codefile.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/codefile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.i
.PHONY : E_/github/astrometry.net_win/solver/codefile.c.i

E_/github/astrometry.net_win/solver/codefile.s: E_/github/astrometry.net_win/solver/codefile.c.s
.PHONY : E_/github/astrometry.net_win/solver/codefile.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/codefile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.s
.PHONY : E_/github/astrometry.net_win/solver/codefile.c.s

E_/github/astrometry.net_win/solver/codetree.obj: E_/github/astrometry.net_win/solver/codetree.c.obj
.PHONY : E_/github/astrometry.net_win/solver/codetree.obj

# target to build an object file
E_/github/astrometry.net_win/solver/codetree.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj
.PHONY : E_/github/astrometry.net_win/solver/codetree.c.obj

E_/github/astrometry.net_win/solver/codetree.i: E_/github/astrometry.net_win/solver/codetree.c.i
.PHONY : E_/github/astrometry.net_win/solver/codetree.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/codetree.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.i
.PHONY : E_/github/astrometry.net_win/solver/codetree.c.i

E_/github/astrometry.net_win/solver/codetree.s: E_/github/astrometry.net_win/solver/codetree.c.s
.PHONY : E_/github/astrometry.net_win/solver/codetree.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/codetree.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.s
.PHONY : E_/github/astrometry.net_win/solver/codetree.c.s

E_/github/astrometry.net_win/solver/cut-table.obj: E_/github/astrometry.net_win/solver/cut-table.c.obj
.PHONY : E_/github/astrometry.net_win/solver/cut-table.obj

# target to build an object file
E_/github/astrometry.net_win/solver/cut-table.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj
.PHONY : E_/github/astrometry.net_win/solver/cut-table.c.obj

E_/github/astrometry.net_win/solver/cut-table.i: E_/github/astrometry.net_win/solver/cut-table.c.i
.PHONY : E_/github/astrometry.net_win/solver/cut-table.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/cut-table.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.i
.PHONY : E_/github/astrometry.net_win/solver/cut-table.c.i

E_/github/astrometry.net_win/solver/cut-table.s: E_/github/astrometry.net_win/solver/cut-table.c.s
.PHONY : E_/github/astrometry.net_win/solver/cut-table.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/cut-table.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.s
.PHONY : E_/github/astrometry.net_win/solver/cut-table.c.s

E_/github/astrometry.net_win/solver/engine-main.obj: E_/github/astrometry.net_win/solver/engine-main.c.obj
.PHONY : E_/github/astrometry.net_win/solver/engine-main.obj

# target to build an object file
E_/github/astrometry.net_win/solver/engine-main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj
.PHONY : E_/github/astrometry.net_win/solver/engine-main.c.obj

E_/github/astrometry.net_win/solver/engine-main.i: E_/github/astrometry.net_win/solver/engine-main.c.i
.PHONY : E_/github/astrometry.net_win/solver/engine-main.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/engine-main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.i
.PHONY : E_/github/astrometry.net_win/solver/engine-main.c.i

E_/github/astrometry.net_win/solver/engine-main.s: E_/github/astrometry.net_win/solver/engine-main.c.s
.PHONY : E_/github/astrometry.net_win/solver/engine-main.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/engine-main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.s
.PHONY : E_/github/astrometry.net_win/solver/engine-main.c.s

E_/github/astrometry.net_win/solver/engine.obj: E_/github/astrometry.net_win/solver/engine.c.obj
.PHONY : E_/github/astrometry.net_win/solver/engine.obj

# target to build an object file
E_/github/astrometry.net_win/solver/engine.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj
.PHONY : E_/github/astrometry.net_win/solver/engine.c.obj

E_/github/astrometry.net_win/solver/engine.i: E_/github/astrometry.net_win/solver/engine.c.i
.PHONY : E_/github/astrometry.net_win/solver/engine.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/engine.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.i
.PHONY : E_/github/astrometry.net_win/solver/engine.c.i

E_/github/astrometry.net_win/solver/engine.s: E_/github/astrometry.net_win/solver/engine.c.s
.PHONY : E_/github/astrometry.net_win/solver/engine.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/engine.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.s
.PHONY : E_/github/astrometry.net_win/solver/engine.c.s

E_/github/astrometry.net_win/solver/fits-guess-scale.obj: E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj
.PHONY : E_/github/astrometry.net_win/solver/fits-guess-scale.obj

# target to build an object file
E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj
.PHONY : E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj

E_/github/astrometry.net_win/solver/fits-guess-scale.i: E_/github/astrometry.net_win/solver/fits-guess-scale.c.i
.PHONY : E_/github/astrometry.net_win/solver/fits-guess-scale.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/fits-guess-scale.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.i
.PHONY : E_/github/astrometry.net_win/solver/fits-guess-scale.c.i

E_/github/astrometry.net_win/solver/fits-guess-scale.s: E_/github/astrometry.net_win/solver/fits-guess-scale.c.s
.PHONY : E_/github/astrometry.net_win/solver/fits-guess-scale.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/fits-guess-scale.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.s
.PHONY : E_/github/astrometry.net_win/solver/fits-guess-scale.c.s

E_/github/astrometry.net_win/solver/hpquads.obj: E_/github/astrometry.net_win/solver/hpquads.c.obj
.PHONY : E_/github/astrometry.net_win/solver/hpquads.obj

# target to build an object file
E_/github/astrometry.net_win/solver/hpquads.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj
.PHONY : E_/github/astrometry.net_win/solver/hpquads.c.obj

E_/github/astrometry.net_win/solver/hpquads.i: E_/github/astrometry.net_win/solver/hpquads.c.i
.PHONY : E_/github/astrometry.net_win/solver/hpquads.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/hpquads.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.i
.PHONY : E_/github/astrometry.net_win/solver/hpquads.c.i

E_/github/astrometry.net_win/solver/hpquads.s: E_/github/astrometry.net_win/solver/hpquads.c.s
.PHONY : E_/github/astrometry.net_win/solver/hpquads.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/hpquads.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.s
.PHONY : E_/github/astrometry.net_win/solver/hpquads.c.s

E_/github/astrometry.net_win/solver/image2xy-files.obj: E_/github/astrometry.net_win/solver/image2xy-files.c.obj
.PHONY : E_/github/astrometry.net_win/solver/image2xy-files.obj

# target to build an object file
E_/github/astrometry.net_win/solver/image2xy-files.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj
.PHONY : E_/github/astrometry.net_win/solver/image2xy-files.c.obj

E_/github/astrometry.net_win/solver/image2xy-files.i: E_/github/astrometry.net_win/solver/image2xy-files.c.i
.PHONY : E_/github/astrometry.net_win/solver/image2xy-files.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/image2xy-files.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.i
.PHONY : E_/github/astrometry.net_win/solver/image2xy-files.c.i

E_/github/astrometry.net_win/solver/image2xy-files.s: E_/github/astrometry.net_win/solver/image2xy-files.c.s
.PHONY : E_/github/astrometry.net_win/solver/image2xy-files.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/image2xy-files.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.s
.PHONY : E_/github/astrometry.net_win/solver/image2xy-files.c.s

E_/github/astrometry.net_win/solver/merge-index.obj: E_/github/astrometry.net_win/solver/merge-index.c.obj
.PHONY : E_/github/astrometry.net_win/solver/merge-index.obj

# target to build an object file
E_/github/astrometry.net_win/solver/merge-index.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj
.PHONY : E_/github/astrometry.net_win/solver/merge-index.c.obj

E_/github/astrometry.net_win/solver/merge-index.i: E_/github/astrometry.net_win/solver/merge-index.c.i
.PHONY : E_/github/astrometry.net_win/solver/merge-index.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/merge-index.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.i
.PHONY : E_/github/astrometry.net_win/solver/merge-index.c.i

E_/github/astrometry.net_win/solver/merge-index.s: E_/github/astrometry.net_win/solver/merge-index.c.s
.PHONY : E_/github/astrometry.net_win/solver/merge-index.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/merge-index.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.s
.PHONY : E_/github/astrometry.net_win/solver/merge-index.c.s

E_/github/astrometry.net_win/solver/new-wcs.obj: E_/github/astrometry.net_win/solver/new-wcs.c.obj
.PHONY : E_/github/astrometry.net_win/solver/new-wcs.obj

# target to build an object file
E_/github/astrometry.net_win/solver/new-wcs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj
.PHONY : E_/github/astrometry.net_win/solver/new-wcs.c.obj

E_/github/astrometry.net_win/solver/new-wcs.i: E_/github/astrometry.net_win/solver/new-wcs.c.i
.PHONY : E_/github/astrometry.net_win/solver/new-wcs.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/new-wcs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.i
.PHONY : E_/github/astrometry.net_win/solver/new-wcs.c.i

E_/github/astrometry.net_win/solver/new-wcs.s: E_/github/astrometry.net_win/solver/new-wcs.c.s
.PHONY : E_/github/astrometry.net_win/solver/new-wcs.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/new-wcs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.s
.PHONY : E_/github/astrometry.net_win/solver/new-wcs.c.s

E_/github/astrometry.net_win/solver/onefield.obj: E_/github/astrometry.net_win/solver/onefield.c.obj
.PHONY : E_/github/astrometry.net_win/solver/onefield.obj

# target to build an object file
E_/github/astrometry.net_win/solver/onefield.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj
.PHONY : E_/github/astrometry.net_win/solver/onefield.c.obj

E_/github/astrometry.net_win/solver/onefield.i: E_/github/astrometry.net_win/solver/onefield.c.i
.PHONY : E_/github/astrometry.net_win/solver/onefield.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/onefield.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.i
.PHONY : E_/github/astrometry.net_win/solver/onefield.c.i

E_/github/astrometry.net_win/solver/onefield.s: E_/github/astrometry.net_win/solver/onefield.c.s
.PHONY : E_/github/astrometry.net_win/solver/onefield.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/onefield.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.s
.PHONY : E_/github/astrometry.net_win/solver/onefield.c.s

E_/github/astrometry.net_win/solver/quad-builder.obj: E_/github/astrometry.net_win/solver/quad-builder.c.obj
.PHONY : E_/github/astrometry.net_win/solver/quad-builder.obj

# target to build an object file
E_/github/astrometry.net_win/solver/quad-builder.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj
.PHONY : E_/github/astrometry.net_win/solver/quad-builder.c.obj

E_/github/astrometry.net_win/solver/quad-builder.i: E_/github/astrometry.net_win/solver/quad-builder.c.i
.PHONY : E_/github/astrometry.net_win/solver/quad-builder.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/quad-builder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.i
.PHONY : E_/github/astrometry.net_win/solver/quad-builder.c.i

E_/github/astrometry.net_win/solver/quad-builder.s: E_/github/astrometry.net_win/solver/quad-builder.c.s
.PHONY : E_/github/astrometry.net_win/solver/quad-builder.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/quad-builder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.s
.PHONY : E_/github/astrometry.net_win/solver/quad-builder.c.s

E_/github/astrometry.net_win/solver/quad-utils.obj: E_/github/astrometry.net_win/solver/quad-utils.c.obj
.PHONY : E_/github/astrometry.net_win/solver/quad-utils.obj

# target to build an object file
E_/github/astrometry.net_win/solver/quad-utils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj
.PHONY : E_/github/astrometry.net_win/solver/quad-utils.c.obj

E_/github/astrometry.net_win/solver/quad-utils.i: E_/github/astrometry.net_win/solver/quad-utils.c.i
.PHONY : E_/github/astrometry.net_win/solver/quad-utils.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/quad-utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.i
.PHONY : E_/github/astrometry.net_win/solver/quad-utils.c.i

E_/github/astrometry.net_win/solver/quad-utils.s: E_/github/astrometry.net_win/solver/quad-utils.c.s
.PHONY : E_/github/astrometry.net_win/solver/quad-utils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/quad-utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.s
.PHONY : E_/github/astrometry.net_win/solver/quad-utils.c.s

E_/github/astrometry.net_win/solver/resort-xylist.obj: E_/github/astrometry.net_win/solver/resort-xylist.c.obj
.PHONY : E_/github/astrometry.net_win/solver/resort-xylist.obj

# target to build an object file
E_/github/astrometry.net_win/solver/resort-xylist.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj
.PHONY : E_/github/astrometry.net_win/solver/resort-xylist.c.obj

E_/github/astrometry.net_win/solver/resort-xylist.i: E_/github/astrometry.net_win/solver/resort-xylist.c.i
.PHONY : E_/github/astrometry.net_win/solver/resort-xylist.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/resort-xylist.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.i
.PHONY : E_/github/astrometry.net_win/solver/resort-xylist.c.i

E_/github/astrometry.net_win/solver/resort-xylist.s: E_/github/astrometry.net_win/solver/resort-xylist.c.s
.PHONY : E_/github/astrometry.net_win/solver/resort-xylist.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/resort-xylist.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.s
.PHONY : E_/github/astrometry.net_win/solver/resort-xylist.c.s

E_/github/astrometry.net_win/solver/solve-field.obj: E_/github/astrometry.net_win/solver/solve-field.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solve-field.obj

# target to build an object file
E_/github/astrometry.net_win/solver/solve-field.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solve-field.c.obj

E_/github/astrometry.net_win/solver/solve-field.i: E_/github/astrometry.net_win/solver/solve-field.c.i
.PHONY : E_/github/astrometry.net_win/solver/solve-field.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/solve-field.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.i
.PHONY : E_/github/astrometry.net_win/solver/solve-field.c.i

E_/github/astrometry.net_win/solver/solve-field.s: E_/github/astrometry.net_win/solver/solve-field.c.s
.PHONY : E_/github/astrometry.net_win/solver/solve-field.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/solve-field.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.s
.PHONY : E_/github/astrometry.net_win/solver/solve-field.c.s

E_/github/astrometry.net_win/solver/solvedfile.obj: E_/github/astrometry.net_win/solver/solvedfile.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solvedfile.obj

# target to build an object file
E_/github/astrometry.net_win/solver/solvedfile.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solvedfile.c.obj

E_/github/astrometry.net_win/solver/solvedfile.i: E_/github/astrometry.net_win/solver/solvedfile.c.i
.PHONY : E_/github/astrometry.net_win/solver/solvedfile.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/solvedfile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.i
.PHONY : E_/github/astrometry.net_win/solver/solvedfile.c.i

E_/github/astrometry.net_win/solver/solvedfile.s: E_/github/astrometry.net_win/solver/solvedfile.c.s
.PHONY : E_/github/astrometry.net_win/solver/solvedfile.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/solvedfile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.s
.PHONY : E_/github/astrometry.net_win/solver/solvedfile.c.s

E_/github/astrometry.net_win/solver/solver.obj: E_/github/astrometry.net_win/solver/solver.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solver.obj

# target to build an object file
E_/github/astrometry.net_win/solver/solver.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solver.c.obj

E_/github/astrometry.net_win/solver/solver.i: E_/github/astrometry.net_win/solver/solver.c.i
.PHONY : E_/github/astrometry.net_win/solver/solver.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/solver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.i
.PHONY : E_/github/astrometry.net_win/solver/solver.c.i

E_/github/astrometry.net_win/solver/solver.s: E_/github/astrometry.net_win/solver/solver.c.s
.PHONY : E_/github/astrometry.net_win/solver/solver.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/solver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.s
.PHONY : E_/github/astrometry.net_win/solver/solver.c.s

E_/github/astrometry.net_win/solver/solverutils.obj: E_/github/astrometry.net_win/solver/solverutils.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solverutils.obj

# target to build an object file
E_/github/astrometry.net_win/solver/solverutils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj
.PHONY : E_/github/astrometry.net_win/solver/solverutils.c.obj

E_/github/astrometry.net_win/solver/solverutils.i: E_/github/astrometry.net_win/solver/solverutils.c.i
.PHONY : E_/github/astrometry.net_win/solver/solverutils.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/solverutils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.i
.PHONY : E_/github/astrometry.net_win/solver/solverutils.c.i

E_/github/astrometry.net_win/solver/solverutils.s: E_/github/astrometry.net_win/solver/solverutils.c.s
.PHONY : E_/github/astrometry.net_win/solver/solverutils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/solverutils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.s
.PHONY : E_/github/astrometry.net_win/solver/solverutils.c.s

E_/github/astrometry.net_win/solver/startree.obj: E_/github/astrometry.net_win/solver/startree.c.obj
.PHONY : E_/github/astrometry.net_win/solver/startree.obj

# target to build an object file
E_/github/astrometry.net_win/solver/startree.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj
.PHONY : E_/github/astrometry.net_win/solver/startree.c.obj

E_/github/astrometry.net_win/solver/startree.i: E_/github/astrometry.net_win/solver/startree.c.i
.PHONY : E_/github/astrometry.net_win/solver/startree.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/startree.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.i
.PHONY : E_/github/astrometry.net_win/solver/startree.c.i

E_/github/astrometry.net_win/solver/startree.s: E_/github/astrometry.net_win/solver/startree.c.s
.PHONY : E_/github/astrometry.net_win/solver/startree.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/startree.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.s
.PHONY : E_/github/astrometry.net_win/solver/startree.c.s

E_/github/astrometry.net_win/solver/tweak.obj: E_/github/astrometry.net_win/solver/tweak.c.obj
.PHONY : E_/github/astrometry.net_win/solver/tweak.obj

# target to build an object file
E_/github/astrometry.net_win/solver/tweak.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj
.PHONY : E_/github/astrometry.net_win/solver/tweak.c.obj

E_/github/astrometry.net_win/solver/tweak.i: E_/github/astrometry.net_win/solver/tweak.c.i
.PHONY : E_/github/astrometry.net_win/solver/tweak.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/tweak.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.i
.PHONY : E_/github/astrometry.net_win/solver/tweak.c.i

E_/github/astrometry.net_win/solver/tweak.s: E_/github/astrometry.net_win/solver/tweak.c.s
.PHONY : E_/github/astrometry.net_win/solver/tweak.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/tweak.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.s
.PHONY : E_/github/astrometry.net_win/solver/tweak.c.s

E_/github/astrometry.net_win/solver/tweak2.obj: E_/github/astrometry.net_win/solver/tweak2.c.obj
.PHONY : E_/github/astrometry.net_win/solver/tweak2.obj

# target to build an object file
E_/github/astrometry.net_win/solver/tweak2.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj
.PHONY : E_/github/astrometry.net_win/solver/tweak2.c.obj

E_/github/astrometry.net_win/solver/tweak2.i: E_/github/astrometry.net_win/solver/tweak2.c.i
.PHONY : E_/github/astrometry.net_win/solver/tweak2.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/tweak2.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.i
.PHONY : E_/github/astrometry.net_win/solver/tweak2.c.i

E_/github/astrometry.net_win/solver/tweak2.s: E_/github/astrometry.net_win/solver/tweak2.c.s
.PHONY : E_/github/astrometry.net_win/solver/tweak2.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/tweak2.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.s
.PHONY : E_/github/astrometry.net_win/solver/tweak2.c.s

E_/github/astrometry.net_win/solver/uniformize-catalog.obj: E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj
.PHONY : E_/github/astrometry.net_win/solver/uniformize-catalog.obj

# target to build an object file
E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj
.PHONY : E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj

E_/github/astrometry.net_win/solver/uniformize-catalog.i: E_/github/astrometry.net_win/solver/uniformize-catalog.c.i
.PHONY : E_/github/astrometry.net_win/solver/uniformize-catalog.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/uniformize-catalog.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.i
.PHONY : E_/github/astrometry.net_win/solver/uniformize-catalog.c.i

E_/github/astrometry.net_win/solver/uniformize-catalog.s: E_/github/astrometry.net_win/solver/uniformize-catalog.c.s
.PHONY : E_/github/astrometry.net_win/solver/uniformize-catalog.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/uniformize-catalog.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.s
.PHONY : E_/github/astrometry.net_win/solver/uniformize-catalog.c.s

E_/github/astrometry.net_win/solver/unpermute-quads.obj: E_/github/astrometry.net_win/solver/unpermute-quads.c.obj
.PHONY : E_/github/astrometry.net_win/solver/unpermute-quads.obj

# target to build an object file
E_/github/astrometry.net_win/solver/unpermute-quads.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj
.PHONY : E_/github/astrometry.net_win/solver/unpermute-quads.c.obj

E_/github/astrometry.net_win/solver/unpermute-quads.i: E_/github/astrometry.net_win/solver/unpermute-quads.c.i
.PHONY : E_/github/astrometry.net_win/solver/unpermute-quads.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/unpermute-quads.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.i
.PHONY : E_/github/astrometry.net_win/solver/unpermute-quads.c.i

E_/github/astrometry.net_win/solver/unpermute-quads.s: E_/github/astrometry.net_win/solver/unpermute-quads.c.s
.PHONY : E_/github/astrometry.net_win/solver/unpermute-quads.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/unpermute-quads.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.s
.PHONY : E_/github/astrometry.net_win/solver/unpermute-quads.c.s

E_/github/astrometry.net_win/solver/unpermute-stars.obj: E_/github/astrometry.net_win/solver/unpermute-stars.c.obj
.PHONY : E_/github/astrometry.net_win/solver/unpermute-stars.obj

# target to build an object file
E_/github/astrometry.net_win/solver/unpermute-stars.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj
.PHONY : E_/github/astrometry.net_win/solver/unpermute-stars.c.obj

E_/github/astrometry.net_win/solver/unpermute-stars.i: E_/github/astrometry.net_win/solver/unpermute-stars.c.i
.PHONY : E_/github/astrometry.net_win/solver/unpermute-stars.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/unpermute-stars.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.i
.PHONY : E_/github/astrometry.net_win/solver/unpermute-stars.c.i

E_/github/astrometry.net_win/solver/unpermute-stars.s: E_/github/astrometry.net_win/solver/unpermute-stars.c.s
.PHONY : E_/github/astrometry.net_win/solver/unpermute-stars.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/unpermute-stars.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.s
.PHONY : E_/github/astrometry.net_win/solver/unpermute-stars.c.s

E_/github/astrometry.net_win/solver/verify.obj: E_/github/astrometry.net_win/solver/verify.c.obj
.PHONY : E_/github/astrometry.net_win/solver/verify.obj

# target to build an object file
E_/github/astrometry.net_win/solver/verify.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj
.PHONY : E_/github/astrometry.net_win/solver/verify.c.obj

E_/github/astrometry.net_win/solver/verify.i: E_/github/astrometry.net_win/solver/verify.c.i
.PHONY : E_/github/astrometry.net_win/solver/verify.i

# target to preprocess a source file
E_/github/astrometry.net_win/solver/verify.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.i
.PHONY : E_/github/astrometry.net_win/solver/verify.c.i

E_/github/astrometry.net_win/solver/verify.s: E_/github/astrometry.net_win/solver/verify.c.s
.PHONY : E_/github/astrometry.net_win/solver/verify.s

# target to generate assembly for a file
E_/github/astrometry.net_win/solver/verify.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.s
.PHONY : E_/github/astrometry.net_win/solver/verify.c.s

E_/github/astrometry.net_win/util/an-endian.obj: E_/github/astrometry.net_win/util/an-endian.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-endian.obj

# target to build an object file
E_/github/astrometry.net_win/util/an-endian.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-endian.c.obj

E_/github/astrometry.net_win/util/an-endian.i: E_/github/astrometry.net_win/util/an-endian.c.i
.PHONY : E_/github/astrometry.net_win/util/an-endian.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/an-endian.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.i
.PHONY : E_/github/astrometry.net_win/util/an-endian.c.i

E_/github/astrometry.net_win/util/an-endian.s: E_/github/astrometry.net_win/util/an-endian.c.s
.PHONY : E_/github/astrometry.net_win/util/an-endian.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/an-endian.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.s
.PHONY : E_/github/astrometry.net_win/util/an-endian.c.s

E_/github/astrometry.net_win/util/an-fitstopnm.obj: E_/github/astrometry.net_win/util/an-fitstopnm.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-fitstopnm.obj

# target to build an object file
E_/github/astrometry.net_win/util/an-fitstopnm.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-fitstopnm.c.obj

E_/github/astrometry.net_win/util/an-fitstopnm.i: E_/github/astrometry.net_win/util/an-fitstopnm.c.i
.PHONY : E_/github/astrometry.net_win/util/an-fitstopnm.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/an-fitstopnm.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.i
.PHONY : E_/github/astrometry.net_win/util/an-fitstopnm.c.i

E_/github/astrometry.net_win/util/an-fitstopnm.s: E_/github/astrometry.net_win/util/an-fitstopnm.c.s
.PHONY : E_/github/astrometry.net_win/util/an-fitstopnm.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/an-fitstopnm.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.s
.PHONY : E_/github/astrometry.net_win/util/an-fitstopnm.c.s

E_/github/astrometry.net_win/util/an-opts.obj: E_/github/astrometry.net_win/util/an-opts.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-opts.obj

# target to build an object file
E_/github/astrometry.net_win/util/an-opts.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-opts.c.obj

E_/github/astrometry.net_win/util/an-opts.i: E_/github/astrometry.net_win/util/an-opts.c.i
.PHONY : E_/github/astrometry.net_win/util/an-opts.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/an-opts.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.i
.PHONY : E_/github/astrometry.net_win/util/an-opts.c.i

E_/github/astrometry.net_win/util/an-opts.s: E_/github/astrometry.net_win/util/an-opts.c.s
.PHONY : E_/github/astrometry.net_win/util/an-opts.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/an-opts.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.s
.PHONY : E_/github/astrometry.net_win/util/an-opts.c.s

E_/github/astrometry.net_win/util/an-pnmtofits.obj: E_/github/astrometry.net_win/util/an-pnmtofits.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-pnmtofits.obj

# target to build an object file
E_/github/astrometry.net_win/util/an-pnmtofits.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj
.PHONY : E_/github/astrometry.net_win/util/an-pnmtofits.c.obj

E_/github/astrometry.net_win/util/an-pnmtofits.i: E_/github/astrometry.net_win/util/an-pnmtofits.c.i
.PHONY : E_/github/astrometry.net_win/util/an-pnmtofits.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/an-pnmtofits.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.i
.PHONY : E_/github/astrometry.net_win/util/an-pnmtofits.c.i

E_/github/astrometry.net_win/util/an-pnmtofits.s: E_/github/astrometry.net_win/util/an-pnmtofits.c.s
.PHONY : E_/github/astrometry.net_win/util/an-pnmtofits.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/an-pnmtofits.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.s
.PHONY : E_/github/astrometry.net_win/util/an-pnmtofits.c.s

E_/github/astrometry.net_win/util/anwcs.obj: E_/github/astrometry.net_win/util/anwcs.c.obj
.PHONY : E_/github/astrometry.net_win/util/anwcs.obj

# target to build an object file
E_/github/astrometry.net_win/util/anwcs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj
.PHONY : E_/github/astrometry.net_win/util/anwcs.c.obj

E_/github/astrometry.net_win/util/anwcs.i: E_/github/astrometry.net_win/util/anwcs.c.i
.PHONY : E_/github/astrometry.net_win/util/anwcs.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/anwcs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.i
.PHONY : E_/github/astrometry.net_win/util/anwcs.c.i

E_/github/astrometry.net_win/util/anwcs.s: E_/github/astrometry.net_win/util/anwcs.c.s
.PHONY : E_/github/astrometry.net_win/util/anwcs.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/anwcs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.s
.PHONY : E_/github/astrometry.net_win/util/anwcs.c.s

E_/github/astrometry.net_win/util/bl-sort.obj: E_/github/astrometry.net_win/util/bl-sort.c.obj
.PHONY : E_/github/astrometry.net_win/util/bl-sort.obj

# target to build an object file
E_/github/astrometry.net_win/util/bl-sort.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj
.PHONY : E_/github/astrometry.net_win/util/bl-sort.c.obj

E_/github/astrometry.net_win/util/bl-sort.i: E_/github/astrometry.net_win/util/bl-sort.c.i
.PHONY : E_/github/astrometry.net_win/util/bl-sort.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/bl-sort.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.i
.PHONY : E_/github/astrometry.net_win/util/bl-sort.c.i

E_/github/astrometry.net_win/util/bl-sort.s: E_/github/astrometry.net_win/util/bl-sort.c.s
.PHONY : E_/github/astrometry.net_win/util/bl-sort.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/bl-sort.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.s
.PHONY : E_/github/astrometry.net_win/util/bl-sort.c.s

E_/github/astrometry.net_win/util/bl.obj: E_/github/astrometry.net_win/util/bl.c.obj
.PHONY : E_/github/astrometry.net_win/util/bl.obj

# target to build an object file
E_/github/astrometry.net_win/util/bl.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj
.PHONY : E_/github/astrometry.net_win/util/bl.c.obj

E_/github/astrometry.net_win/util/bl.i: E_/github/astrometry.net_win/util/bl.c.i
.PHONY : E_/github/astrometry.net_win/util/bl.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/bl.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.i
.PHONY : E_/github/astrometry.net_win/util/bl.c.i

E_/github/astrometry.net_win/util/bl.s: E_/github/astrometry.net_win/util/bl.c.s
.PHONY : E_/github/astrometry.net_win/util/bl.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/bl.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.s
.PHONY : E_/github/astrometry.net_win/util/bl.c.s

E_/github/astrometry.net_win/util/codekd.obj: E_/github/astrometry.net_win/util/codekd.c.obj
.PHONY : E_/github/astrometry.net_win/util/codekd.obj

# target to build an object file
E_/github/astrometry.net_win/util/codekd.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj
.PHONY : E_/github/astrometry.net_win/util/codekd.c.obj

E_/github/astrometry.net_win/util/codekd.i: E_/github/astrometry.net_win/util/codekd.c.i
.PHONY : E_/github/astrometry.net_win/util/codekd.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/codekd.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.i
.PHONY : E_/github/astrometry.net_win/util/codekd.c.i

E_/github/astrometry.net_win/util/codekd.s: E_/github/astrometry.net_win/util/codekd.c.s
.PHONY : E_/github/astrometry.net_win/util/codekd.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/codekd.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.s
.PHONY : E_/github/astrometry.net_win/util/codekd.c.s

E_/github/astrometry.net_win/util/ctmf.obj: E_/github/astrometry.net_win/util/ctmf.c.obj
.PHONY : E_/github/astrometry.net_win/util/ctmf.obj

# target to build an object file
E_/github/astrometry.net_win/util/ctmf.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj
.PHONY : E_/github/astrometry.net_win/util/ctmf.c.obj

E_/github/astrometry.net_win/util/ctmf.i: E_/github/astrometry.net_win/util/ctmf.c.i
.PHONY : E_/github/astrometry.net_win/util/ctmf.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/ctmf.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.i
.PHONY : E_/github/astrometry.net_win/util/ctmf.c.i

E_/github/astrometry.net_win/util/ctmf.s: E_/github/astrometry.net_win/util/ctmf.c.s
.PHONY : E_/github/astrometry.net_win/util/ctmf.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/ctmf.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.s
.PHONY : E_/github/astrometry.net_win/util/ctmf.c.s

E_/github/astrometry.net_win/util/dallpeaks.obj: E_/github/astrometry.net_win/util/dallpeaks.c.obj
.PHONY : E_/github/astrometry.net_win/util/dallpeaks.obj

# target to build an object file
E_/github/astrometry.net_win/util/dallpeaks.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj
.PHONY : E_/github/astrometry.net_win/util/dallpeaks.c.obj

E_/github/astrometry.net_win/util/dallpeaks.i: E_/github/astrometry.net_win/util/dallpeaks.c.i
.PHONY : E_/github/astrometry.net_win/util/dallpeaks.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dallpeaks.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.i
.PHONY : E_/github/astrometry.net_win/util/dallpeaks.c.i

E_/github/astrometry.net_win/util/dallpeaks.s: E_/github/astrometry.net_win/util/dallpeaks.c.s
.PHONY : E_/github/astrometry.net_win/util/dallpeaks.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dallpeaks.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.s
.PHONY : E_/github/astrometry.net_win/util/dallpeaks.c.s

E_/github/astrometry.net_win/util/datalog.obj: E_/github/astrometry.net_win/util/datalog.c.obj
.PHONY : E_/github/astrometry.net_win/util/datalog.obj

# target to build an object file
E_/github/astrometry.net_win/util/datalog.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj
.PHONY : E_/github/astrometry.net_win/util/datalog.c.obj

E_/github/astrometry.net_win/util/datalog.i: E_/github/astrometry.net_win/util/datalog.c.i
.PHONY : E_/github/astrometry.net_win/util/datalog.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/datalog.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.i
.PHONY : E_/github/astrometry.net_win/util/datalog.c.i

E_/github/astrometry.net_win/util/datalog.s: E_/github/astrometry.net_win/util/datalog.c.s
.PHONY : E_/github/astrometry.net_win/util/datalog.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/datalog.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.s
.PHONY : E_/github/astrometry.net_win/util/datalog.c.s

E_/github/astrometry.net_win/util/dcen3x3.obj: E_/github/astrometry.net_win/util/dcen3x3.c.obj
.PHONY : E_/github/astrometry.net_win/util/dcen3x3.obj

# target to build an object file
E_/github/astrometry.net_win/util/dcen3x3.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj
.PHONY : E_/github/astrometry.net_win/util/dcen3x3.c.obj

E_/github/astrometry.net_win/util/dcen3x3.i: E_/github/astrometry.net_win/util/dcen3x3.c.i
.PHONY : E_/github/astrometry.net_win/util/dcen3x3.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dcen3x3.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.i
.PHONY : E_/github/astrometry.net_win/util/dcen3x3.c.i

E_/github/astrometry.net_win/util/dcen3x3.s: E_/github/astrometry.net_win/util/dcen3x3.c.s
.PHONY : E_/github/astrometry.net_win/util/dcen3x3.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dcen3x3.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.s
.PHONY : E_/github/astrometry.net_win/util/dcen3x3.c.s

E_/github/astrometry.net_win/util/dfind.obj: E_/github/astrometry.net_win/util/dfind.c.obj
.PHONY : E_/github/astrometry.net_win/util/dfind.obj

# target to build an object file
E_/github/astrometry.net_win/util/dfind.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj
.PHONY : E_/github/astrometry.net_win/util/dfind.c.obj

E_/github/astrometry.net_win/util/dfind.i: E_/github/astrometry.net_win/util/dfind.c.i
.PHONY : E_/github/astrometry.net_win/util/dfind.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dfind.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.i
.PHONY : E_/github/astrometry.net_win/util/dfind.c.i

E_/github/astrometry.net_win/util/dfind.s: E_/github/astrometry.net_win/util/dfind.c.s
.PHONY : E_/github/astrometry.net_win/util/dfind.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dfind.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.s
.PHONY : E_/github/astrometry.net_win/util/dfind.c.s

E_/github/astrometry.net_win/util/dmedsmooth.obj: E_/github/astrometry.net_win/util/dmedsmooth.c.obj
.PHONY : E_/github/astrometry.net_win/util/dmedsmooth.obj

# target to build an object file
E_/github/astrometry.net_win/util/dmedsmooth.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj
.PHONY : E_/github/astrometry.net_win/util/dmedsmooth.c.obj

E_/github/astrometry.net_win/util/dmedsmooth.i: E_/github/astrometry.net_win/util/dmedsmooth.c.i
.PHONY : E_/github/astrometry.net_win/util/dmedsmooth.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dmedsmooth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.i
.PHONY : E_/github/astrometry.net_win/util/dmedsmooth.c.i

E_/github/astrometry.net_win/util/dmedsmooth.s: E_/github/astrometry.net_win/util/dmedsmooth.c.s
.PHONY : E_/github/astrometry.net_win/util/dmedsmooth.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dmedsmooth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.s
.PHONY : E_/github/astrometry.net_win/util/dmedsmooth.c.s

E_/github/astrometry.net_win/util/dobjects.obj: E_/github/astrometry.net_win/util/dobjects.c.obj
.PHONY : E_/github/astrometry.net_win/util/dobjects.obj

# target to build an object file
E_/github/astrometry.net_win/util/dobjects.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj
.PHONY : E_/github/astrometry.net_win/util/dobjects.c.obj

E_/github/astrometry.net_win/util/dobjects.i: E_/github/astrometry.net_win/util/dobjects.c.i
.PHONY : E_/github/astrometry.net_win/util/dobjects.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dobjects.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.i
.PHONY : E_/github/astrometry.net_win/util/dobjects.c.i

E_/github/astrometry.net_win/util/dobjects.s: E_/github/astrometry.net_win/util/dobjects.c.s
.PHONY : E_/github/astrometry.net_win/util/dobjects.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dobjects.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.s
.PHONY : E_/github/astrometry.net_win/util/dobjects.c.s

E_/github/astrometry.net_win/util/dpeaks.obj: E_/github/astrometry.net_win/util/dpeaks.c.obj
.PHONY : E_/github/astrometry.net_win/util/dpeaks.obj

# target to build an object file
E_/github/astrometry.net_win/util/dpeaks.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj
.PHONY : E_/github/astrometry.net_win/util/dpeaks.c.obj

E_/github/astrometry.net_win/util/dpeaks.i: E_/github/astrometry.net_win/util/dpeaks.c.i
.PHONY : E_/github/astrometry.net_win/util/dpeaks.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dpeaks.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.i
.PHONY : E_/github/astrometry.net_win/util/dpeaks.c.i

E_/github/astrometry.net_win/util/dpeaks.s: E_/github/astrometry.net_win/util/dpeaks.c.s
.PHONY : E_/github/astrometry.net_win/util/dpeaks.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dpeaks.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.s
.PHONY : E_/github/astrometry.net_win/util/dpeaks.c.s

E_/github/astrometry.net_win/util/dselip.obj: E_/github/astrometry.net_win/util/dselip.c.obj
.PHONY : E_/github/astrometry.net_win/util/dselip.obj

# target to build an object file
E_/github/astrometry.net_win/util/dselip.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj
.PHONY : E_/github/astrometry.net_win/util/dselip.c.obj

E_/github/astrometry.net_win/util/dselip.i: E_/github/astrometry.net_win/util/dselip.c.i
.PHONY : E_/github/astrometry.net_win/util/dselip.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dselip.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.i
.PHONY : E_/github/astrometry.net_win/util/dselip.c.i

E_/github/astrometry.net_win/util/dselip.s: E_/github/astrometry.net_win/util/dselip.c.s
.PHONY : E_/github/astrometry.net_win/util/dselip.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dselip.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.s
.PHONY : E_/github/astrometry.net_win/util/dselip.c.s

E_/github/astrometry.net_win/util/dsigma.obj: E_/github/astrometry.net_win/util/dsigma.c.obj
.PHONY : E_/github/astrometry.net_win/util/dsigma.obj

# target to build an object file
E_/github/astrometry.net_win/util/dsigma.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj
.PHONY : E_/github/astrometry.net_win/util/dsigma.c.obj

E_/github/astrometry.net_win/util/dsigma.i: E_/github/astrometry.net_win/util/dsigma.c.i
.PHONY : E_/github/astrometry.net_win/util/dsigma.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dsigma.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.i
.PHONY : E_/github/astrometry.net_win/util/dsigma.c.i

E_/github/astrometry.net_win/util/dsigma.s: E_/github/astrometry.net_win/util/dsigma.c.s
.PHONY : E_/github/astrometry.net_win/util/dsigma.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dsigma.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.s
.PHONY : E_/github/astrometry.net_win/util/dsigma.c.s

E_/github/astrometry.net_win/util/dsmooth.obj: E_/github/astrometry.net_win/util/dsmooth.c.obj
.PHONY : E_/github/astrometry.net_win/util/dsmooth.obj

# target to build an object file
E_/github/astrometry.net_win/util/dsmooth.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj
.PHONY : E_/github/astrometry.net_win/util/dsmooth.c.obj

E_/github/astrometry.net_win/util/dsmooth.i: E_/github/astrometry.net_win/util/dsmooth.c.i
.PHONY : E_/github/astrometry.net_win/util/dsmooth.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/dsmooth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.i
.PHONY : E_/github/astrometry.net_win/util/dsmooth.c.i

E_/github/astrometry.net_win/util/dsmooth.s: E_/github/astrometry.net_win/util/dsmooth.c.s
.PHONY : E_/github/astrometry.net_win/util/dsmooth.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/dsmooth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.s
.PHONY : E_/github/astrometry.net_win/util/dsmooth.c.s

E_/github/astrometry.net_win/util/errors.obj: E_/github/astrometry.net_win/util/errors.c.obj
.PHONY : E_/github/astrometry.net_win/util/errors.obj

# target to build an object file
E_/github/astrometry.net_win/util/errors.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj
.PHONY : E_/github/astrometry.net_win/util/errors.c.obj

E_/github/astrometry.net_win/util/errors.i: E_/github/astrometry.net_win/util/errors.c.i
.PHONY : E_/github/astrometry.net_win/util/errors.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/errors.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.i
.PHONY : E_/github/astrometry.net_win/util/errors.c.i

E_/github/astrometry.net_win/util/errors.s: E_/github/astrometry.net_win/util/errors.c.s
.PHONY : E_/github/astrometry.net_win/util/errors.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/errors.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.s
.PHONY : E_/github/astrometry.net_win/util/errors.c.s

E_/github/astrometry.net_win/util/fileutils.obj: E_/github/astrometry.net_win/util/fileutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/fileutils.obj

# target to build an object file
E_/github/astrometry.net_win/util/fileutils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/fileutils.c.obj

E_/github/astrometry.net_win/util/fileutils.i: E_/github/astrometry.net_win/util/fileutils.c.i
.PHONY : E_/github/astrometry.net_win/util/fileutils.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/fileutils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.i
.PHONY : E_/github/astrometry.net_win/util/fileutils.c.i

E_/github/astrometry.net_win/util/fileutils.s: E_/github/astrometry.net_win/util/fileutils.c.s
.PHONY : E_/github/astrometry.net_win/util/fileutils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/fileutils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.s
.PHONY : E_/github/astrometry.net_win/util/fileutils.c.s

E_/github/astrometry.net_win/util/fit-wcs.obj: E_/github/astrometry.net_win/util/fit-wcs.c.obj
.PHONY : E_/github/astrometry.net_win/util/fit-wcs.obj

# target to build an object file
E_/github/astrometry.net_win/util/fit-wcs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj
.PHONY : E_/github/astrometry.net_win/util/fit-wcs.c.obj

E_/github/astrometry.net_win/util/fit-wcs.i: E_/github/astrometry.net_win/util/fit-wcs.c.i
.PHONY : E_/github/astrometry.net_win/util/fit-wcs.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/fit-wcs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.i
.PHONY : E_/github/astrometry.net_win/util/fit-wcs.c.i

E_/github/astrometry.net_win/util/fit-wcs.s: E_/github/astrometry.net_win/util/fit-wcs.c.s
.PHONY : E_/github/astrometry.net_win/util/fit-wcs.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/fit-wcs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.s
.PHONY : E_/github/astrometry.net_win/util/fit-wcs.c.s

E_/github/astrometry.net_win/util/fitsbin.obj: E_/github/astrometry.net_win/util/fitsbin.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitsbin.obj

# target to build an object file
E_/github/astrometry.net_win/util/fitsbin.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitsbin.c.obj

E_/github/astrometry.net_win/util/fitsbin.i: E_/github/astrometry.net_win/util/fitsbin.c.i
.PHONY : E_/github/astrometry.net_win/util/fitsbin.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/fitsbin.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.i
.PHONY : E_/github/astrometry.net_win/util/fitsbin.c.i

E_/github/astrometry.net_win/util/fitsbin.s: E_/github/astrometry.net_win/util/fitsbin.c.s
.PHONY : E_/github/astrometry.net_win/util/fitsbin.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/fitsbin.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.s
.PHONY : E_/github/astrometry.net_win/util/fitsbin.c.s

E_/github/astrometry.net_win/util/fitsfile.obj: E_/github/astrometry.net_win/util/fitsfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitsfile.obj

# target to build an object file
E_/github/astrometry.net_win/util/fitsfile.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitsfile.c.obj

E_/github/astrometry.net_win/util/fitsfile.i: E_/github/astrometry.net_win/util/fitsfile.c.i
.PHONY : E_/github/astrometry.net_win/util/fitsfile.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/fitsfile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.i
.PHONY : E_/github/astrometry.net_win/util/fitsfile.c.i

E_/github/astrometry.net_win/util/fitsfile.s: E_/github/astrometry.net_win/util/fitsfile.c.s
.PHONY : E_/github/astrometry.net_win/util/fitsfile.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/fitsfile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.s
.PHONY : E_/github/astrometry.net_win/util/fitsfile.c.s

E_/github/astrometry.net_win/util/fitsioutils.obj: E_/github/astrometry.net_win/util/fitsioutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitsioutils.obj

# target to build an object file
E_/github/astrometry.net_win/util/fitsioutils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitsioutils.c.obj

E_/github/astrometry.net_win/util/fitsioutils.i: E_/github/astrometry.net_win/util/fitsioutils.c.i
.PHONY : E_/github/astrometry.net_win/util/fitsioutils.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/fitsioutils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.i
.PHONY : E_/github/astrometry.net_win/util/fitsioutils.c.i

E_/github/astrometry.net_win/util/fitsioutils.s: E_/github/astrometry.net_win/util/fitsioutils.c.s
.PHONY : E_/github/astrometry.net_win/util/fitsioutils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/fitsioutils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.s
.PHONY : E_/github/astrometry.net_win/util/fitsioutils.c.s

E_/github/astrometry.net_win/util/fitstable.obj: E_/github/astrometry.net_win/util/fitstable.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitstable.obj

# target to build an object file
E_/github/astrometry.net_win/util/fitstable.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj
.PHONY : E_/github/astrometry.net_win/util/fitstable.c.obj

E_/github/astrometry.net_win/util/fitstable.i: E_/github/astrometry.net_win/util/fitstable.c.i
.PHONY : E_/github/astrometry.net_win/util/fitstable.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/fitstable.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.i
.PHONY : E_/github/astrometry.net_win/util/fitstable.c.i

E_/github/astrometry.net_win/util/fitstable.s: E_/github/astrometry.net_win/util/fitstable.c.s
.PHONY : E_/github/astrometry.net_win/util/fitstable.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/fitstable.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.s
.PHONY : E_/github/astrometry.net_win/util/fitstable.c.s

E_/github/astrometry.net_win/util/gslutils.obj: E_/github/astrometry.net_win/util/gslutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/gslutils.obj

# target to build an object file
E_/github/astrometry.net_win/util/gslutils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/gslutils.c.obj

E_/github/astrometry.net_win/util/gslutils.i: E_/github/astrometry.net_win/util/gslutils.c.i
.PHONY : E_/github/astrometry.net_win/util/gslutils.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/gslutils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.i
.PHONY : E_/github/astrometry.net_win/util/gslutils.c.i

E_/github/astrometry.net_win/util/gslutils.s: E_/github/astrometry.net_win/util/gslutils.c.s
.PHONY : E_/github/astrometry.net_win/util/gslutils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/gslutils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.s
.PHONY : E_/github/astrometry.net_win/util/gslutils.c.s

E_/github/astrometry.net_win/util/healpix-utils.obj: E_/github/astrometry.net_win/util/healpix-utils.c.obj
.PHONY : E_/github/astrometry.net_win/util/healpix-utils.obj

# target to build an object file
E_/github/astrometry.net_win/util/healpix-utils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix-utils.c.obj
.PHONY : E_/github/astrometry.net_win/util/healpix-utils.c.obj

E_/github/astrometry.net_win/util/healpix-utils.i: E_/github/astrometry.net_win/util/healpix-utils.c.i
.PHONY : E_/github/astrometry.net_win/util/healpix-utils.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/healpix-utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix-utils.c.i
.PHONY : E_/github/astrometry.net_win/util/healpix-utils.c.i

E_/github/astrometry.net_win/util/healpix-utils.s: E_/github/astrometry.net_win/util/healpix-utils.c.s
.PHONY : E_/github/astrometry.net_win/util/healpix-utils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/healpix-utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix-utils.c.s
.PHONY : E_/github/astrometry.net_win/util/healpix-utils.c.s

E_/github/astrometry.net_win/util/healpix.obj: E_/github/astrometry.net_win/util/healpix.c.obj
.PHONY : E_/github/astrometry.net_win/util/healpix.obj

# target to build an object file
E_/github/astrometry.net_win/util/healpix.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj
.PHONY : E_/github/astrometry.net_win/util/healpix.c.obj

E_/github/astrometry.net_win/util/healpix.i: E_/github/astrometry.net_win/util/healpix.c.i
.PHONY : E_/github/astrometry.net_win/util/healpix.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/healpix.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.i
.PHONY : E_/github/astrometry.net_win/util/healpix.c.i

E_/github/astrometry.net_win/util/healpix.s: E_/github/astrometry.net_win/util/healpix.c.s
.PHONY : E_/github/astrometry.net_win/util/healpix.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/healpix.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.s
.PHONY : E_/github/astrometry.net_win/util/healpix.c.s

E_/github/astrometry.net_win/util/image2xy.obj: E_/github/astrometry.net_win/util/image2xy.c.obj
.PHONY : E_/github/astrometry.net_win/util/image2xy.obj

# target to build an object file
E_/github/astrometry.net_win/util/image2xy.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj
.PHONY : E_/github/astrometry.net_win/util/image2xy.c.obj

E_/github/astrometry.net_win/util/image2xy.i: E_/github/astrometry.net_win/util/image2xy.c.i
.PHONY : E_/github/astrometry.net_win/util/image2xy.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/image2xy.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.i
.PHONY : E_/github/astrometry.net_win/util/image2xy.c.i

E_/github/astrometry.net_win/util/image2xy.s: E_/github/astrometry.net_win/util/image2xy.c.s
.PHONY : E_/github/astrometry.net_win/util/image2xy.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/image2xy.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.s
.PHONY : E_/github/astrometry.net_win/util/image2xy.c.s

E_/github/astrometry.net_win/util/index.obj: E_/github/astrometry.net_win/util/index.c.obj
.PHONY : E_/github/astrometry.net_win/util/index.obj

# target to build an object file
E_/github/astrometry.net_win/util/index.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj
.PHONY : E_/github/astrometry.net_win/util/index.c.obj

E_/github/astrometry.net_win/util/index.i: E_/github/astrometry.net_win/util/index.c.i
.PHONY : E_/github/astrometry.net_win/util/index.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/index.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.i
.PHONY : E_/github/astrometry.net_win/util/index.c.i

E_/github/astrometry.net_win/util/index.s: E_/github/astrometry.net_win/util/index.c.s
.PHONY : E_/github/astrometry.net_win/util/index.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/index.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.s
.PHONY : E_/github/astrometry.net_win/util/index.c.s

E_/github/astrometry.net_win/util/indexset.obj: E_/github/astrometry.net_win/util/indexset.c.obj
.PHONY : E_/github/astrometry.net_win/util/indexset.obj

# target to build an object file
E_/github/astrometry.net_win/util/indexset.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj
.PHONY : E_/github/astrometry.net_win/util/indexset.c.obj

E_/github/astrometry.net_win/util/indexset.i: E_/github/astrometry.net_win/util/indexset.c.i
.PHONY : E_/github/astrometry.net_win/util/indexset.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/indexset.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.i
.PHONY : E_/github/astrometry.net_win/util/indexset.c.i

E_/github/astrometry.net_win/util/indexset.s: E_/github/astrometry.net_win/util/indexset.c.s
.PHONY : E_/github/astrometry.net_win/util/indexset.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/indexset.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.s
.PHONY : E_/github/astrometry.net_win/util/indexset.c.s

E_/github/astrometry.net_win/util/ioutils.obj: E_/github/astrometry.net_win/util/ioutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/ioutils.obj

# target to build an object file
E_/github/astrometry.net_win/util/ioutils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj
.PHONY : E_/github/astrometry.net_win/util/ioutils.c.obj

E_/github/astrometry.net_win/util/ioutils.i: E_/github/astrometry.net_win/util/ioutils.c.i
.PHONY : E_/github/astrometry.net_win/util/ioutils.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/ioutils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.i
.PHONY : E_/github/astrometry.net_win/util/ioutils.c.i

E_/github/astrometry.net_win/util/ioutils.s: E_/github/astrometry.net_win/util/ioutils.c.s
.PHONY : E_/github/astrometry.net_win/util/ioutils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/ioutils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.s
.PHONY : E_/github/astrometry.net_win/util/ioutils.c.s

E_/github/astrometry.net_win/util/log.obj: E_/github/astrometry.net_win/util/log.c.obj
.PHONY : E_/github/astrometry.net_win/util/log.obj

# target to build an object file
E_/github/astrometry.net_win/util/log.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj
.PHONY : E_/github/astrometry.net_win/util/log.c.obj

E_/github/astrometry.net_win/util/log.i: E_/github/astrometry.net_win/util/log.c.i
.PHONY : E_/github/astrometry.net_win/util/log.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/log.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.i
.PHONY : E_/github/astrometry.net_win/util/log.c.i

E_/github/astrometry.net_win/util/log.s: E_/github/astrometry.net_win/util/log.c.s
.PHONY : E_/github/astrometry.net_win/util/log.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/log.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.s
.PHONY : E_/github/astrometry.net_win/util/log.c.s

E_/github/astrometry.net_win/util/matchfile.obj: E_/github/astrometry.net_win/util/matchfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/matchfile.obj

# target to build an object file
E_/github/astrometry.net_win/util/matchfile.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/matchfile.c.obj

E_/github/astrometry.net_win/util/matchfile.i: E_/github/astrometry.net_win/util/matchfile.c.i
.PHONY : E_/github/astrometry.net_win/util/matchfile.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/matchfile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.i
.PHONY : E_/github/astrometry.net_win/util/matchfile.c.i

E_/github/astrometry.net_win/util/matchfile.s: E_/github/astrometry.net_win/util/matchfile.c.s
.PHONY : E_/github/astrometry.net_win/util/matchfile.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/matchfile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.s
.PHONY : E_/github/astrometry.net_win/util/matchfile.c.s

E_/github/astrometry.net_win/util/matchobj.obj: E_/github/astrometry.net_win/util/matchobj.c.obj
.PHONY : E_/github/astrometry.net_win/util/matchobj.obj

# target to build an object file
E_/github/astrometry.net_win/util/matchobj.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj
.PHONY : E_/github/astrometry.net_win/util/matchobj.c.obj

E_/github/astrometry.net_win/util/matchobj.i: E_/github/astrometry.net_win/util/matchobj.c.i
.PHONY : E_/github/astrometry.net_win/util/matchobj.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/matchobj.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.i
.PHONY : E_/github/astrometry.net_win/util/matchobj.c.i

E_/github/astrometry.net_win/util/matchobj.s: E_/github/astrometry.net_win/util/matchobj.c.s
.PHONY : E_/github/astrometry.net_win/util/matchobj.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/matchobj.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.s
.PHONY : E_/github/astrometry.net_win/util/matchobj.c.s

E_/github/astrometry.net_win/util/mathutil.obj: E_/github/astrometry.net_win/util/mathutil.c.obj
.PHONY : E_/github/astrometry.net_win/util/mathutil.obj

# target to build an object file
E_/github/astrometry.net_win/util/mathutil.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj
.PHONY : E_/github/astrometry.net_win/util/mathutil.c.obj

E_/github/astrometry.net_win/util/mathutil.i: E_/github/astrometry.net_win/util/mathutil.c.i
.PHONY : E_/github/astrometry.net_win/util/mathutil.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/mathutil.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.i
.PHONY : E_/github/astrometry.net_win/util/mathutil.c.i

E_/github/astrometry.net_win/util/mathutil.s: E_/github/astrometry.net_win/util/mathutil.c.s
.PHONY : E_/github/astrometry.net_win/util/mathutil.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/mathutil.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.s
.PHONY : E_/github/astrometry.net_win/util/mathutil.c.s

E_/github/astrometry.net_win/util/multiindex.obj: E_/github/astrometry.net_win/util/multiindex.c.obj
.PHONY : E_/github/astrometry.net_win/util/multiindex.obj

# target to build an object file
E_/github/astrometry.net_win/util/multiindex.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj
.PHONY : E_/github/astrometry.net_win/util/multiindex.c.obj

E_/github/astrometry.net_win/util/multiindex.i: E_/github/astrometry.net_win/util/multiindex.c.i
.PHONY : E_/github/astrometry.net_win/util/multiindex.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/multiindex.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.i
.PHONY : E_/github/astrometry.net_win/util/multiindex.c.i

E_/github/astrometry.net_win/util/multiindex.s: E_/github/astrometry.net_win/util/multiindex.c.s
.PHONY : E_/github/astrometry.net_win/util/multiindex.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/multiindex.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.s
.PHONY : E_/github/astrometry.net_win/util/multiindex.c.s

E_/github/astrometry.net_win/util/permutedsort.obj: E_/github/astrometry.net_win/util/permutedsort.c.obj
.PHONY : E_/github/astrometry.net_win/util/permutedsort.obj

# target to build an object file
E_/github/astrometry.net_win/util/permutedsort.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj
.PHONY : E_/github/astrometry.net_win/util/permutedsort.c.obj

E_/github/astrometry.net_win/util/permutedsort.i: E_/github/astrometry.net_win/util/permutedsort.c.i
.PHONY : E_/github/astrometry.net_win/util/permutedsort.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/permutedsort.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.i
.PHONY : E_/github/astrometry.net_win/util/permutedsort.c.i

E_/github/astrometry.net_win/util/permutedsort.s: E_/github/astrometry.net_win/util/permutedsort.c.s
.PHONY : E_/github/astrometry.net_win/util/permutedsort.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/permutedsort.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.s
.PHONY : E_/github/astrometry.net_win/util/permutedsort.c.s

E_/github/astrometry.net_win/util/qidxfile.obj: E_/github/astrometry.net_win/util/qidxfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/qidxfile.obj

# target to build an object file
E_/github/astrometry.net_win/util/qidxfile.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/qidxfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/qidxfile.c.obj

E_/github/astrometry.net_win/util/qidxfile.i: E_/github/astrometry.net_win/util/qidxfile.c.i
.PHONY : E_/github/astrometry.net_win/util/qidxfile.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/qidxfile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/qidxfile.c.i
.PHONY : E_/github/astrometry.net_win/util/qidxfile.c.i

E_/github/astrometry.net_win/util/qidxfile.s: E_/github/astrometry.net_win/util/qidxfile.c.s
.PHONY : E_/github/astrometry.net_win/util/qidxfile.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/qidxfile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/qidxfile.c.s
.PHONY : E_/github/astrometry.net_win/util/qidxfile.c.s

E_/github/astrometry.net_win/util/quadfile.obj: E_/github/astrometry.net_win/util/quadfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/quadfile.obj

# target to build an object file
E_/github/astrometry.net_win/util/quadfile.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj
.PHONY : E_/github/astrometry.net_win/util/quadfile.c.obj

E_/github/astrometry.net_win/util/quadfile.i: E_/github/astrometry.net_win/util/quadfile.c.i
.PHONY : E_/github/astrometry.net_win/util/quadfile.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/quadfile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.i
.PHONY : E_/github/astrometry.net_win/util/quadfile.c.i

E_/github/astrometry.net_win/util/quadfile.s: E_/github/astrometry.net_win/util/quadfile.c.s
.PHONY : E_/github/astrometry.net_win/util/quadfile.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/quadfile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.s
.PHONY : E_/github/astrometry.net_win/util/quadfile.c.s

E_/github/astrometry.net_win/util/rdlist.obj: E_/github/astrometry.net_win/util/rdlist.c.obj
.PHONY : E_/github/astrometry.net_win/util/rdlist.obj

# target to build an object file
E_/github/astrometry.net_win/util/rdlist.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj
.PHONY : E_/github/astrometry.net_win/util/rdlist.c.obj

E_/github/astrometry.net_win/util/rdlist.i: E_/github/astrometry.net_win/util/rdlist.c.i
.PHONY : E_/github/astrometry.net_win/util/rdlist.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/rdlist.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.i
.PHONY : E_/github/astrometry.net_win/util/rdlist.c.i

E_/github/astrometry.net_win/util/rdlist.s: E_/github/astrometry.net_win/util/rdlist.c.s
.PHONY : E_/github/astrometry.net_win/util/rdlist.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/rdlist.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.s
.PHONY : E_/github/astrometry.net_win/util/rdlist.c.s

E_/github/astrometry.net_win/util/resample.obj: E_/github/astrometry.net_win/util/resample.c.obj
.PHONY : E_/github/astrometry.net_win/util/resample.obj

# target to build an object file
E_/github/astrometry.net_win/util/resample.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj
.PHONY : E_/github/astrometry.net_win/util/resample.c.obj

E_/github/astrometry.net_win/util/resample.i: E_/github/astrometry.net_win/util/resample.c.i
.PHONY : E_/github/astrometry.net_win/util/resample.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/resample.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.i
.PHONY : E_/github/astrometry.net_win/util/resample.c.i

E_/github/astrometry.net_win/util/resample.s: E_/github/astrometry.net_win/util/resample.c.s
.PHONY : E_/github/astrometry.net_win/util/resample.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/resample.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.s
.PHONY : E_/github/astrometry.net_win/util/resample.c.s

E_/github/astrometry.net_win/util/scamp-catalog.obj: E_/github/astrometry.net_win/util/scamp-catalog.c.obj
.PHONY : E_/github/astrometry.net_win/util/scamp-catalog.obj

# target to build an object file
E_/github/astrometry.net_win/util/scamp-catalog.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj
.PHONY : E_/github/astrometry.net_win/util/scamp-catalog.c.obj

E_/github/astrometry.net_win/util/scamp-catalog.i: E_/github/astrometry.net_win/util/scamp-catalog.c.i
.PHONY : E_/github/astrometry.net_win/util/scamp-catalog.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/scamp-catalog.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.i
.PHONY : E_/github/astrometry.net_win/util/scamp-catalog.c.i

E_/github/astrometry.net_win/util/scamp-catalog.s: E_/github/astrometry.net_win/util/scamp-catalog.c.s
.PHONY : E_/github/astrometry.net_win/util/scamp-catalog.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/scamp-catalog.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.s
.PHONY : E_/github/astrometry.net_win/util/scamp-catalog.c.s

E_/github/astrometry.net_win/util/scamp.obj: E_/github/astrometry.net_win/util/scamp.c.obj
.PHONY : E_/github/astrometry.net_win/util/scamp.obj

# target to build an object file
E_/github/astrometry.net_win/util/scamp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj
.PHONY : E_/github/astrometry.net_win/util/scamp.c.obj

E_/github/astrometry.net_win/util/scamp.i: E_/github/astrometry.net_win/util/scamp.c.i
.PHONY : E_/github/astrometry.net_win/util/scamp.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/scamp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.i
.PHONY : E_/github/astrometry.net_win/util/scamp.c.i

E_/github/astrometry.net_win/util/scamp.s: E_/github/astrometry.net_win/util/scamp.c.s
.PHONY : E_/github/astrometry.net_win/util/scamp.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/scamp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.s
.PHONY : E_/github/astrometry.net_win/util/scamp.c.s

E_/github/astrometry.net_win/util/simplexy.obj: E_/github/astrometry.net_win/util/simplexy.c.obj
.PHONY : E_/github/astrometry.net_win/util/simplexy.obj

# target to build an object file
E_/github/astrometry.net_win/util/simplexy.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj
.PHONY : E_/github/astrometry.net_win/util/simplexy.c.obj

E_/github/astrometry.net_win/util/simplexy.i: E_/github/astrometry.net_win/util/simplexy.c.i
.PHONY : E_/github/astrometry.net_win/util/simplexy.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/simplexy.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.i
.PHONY : E_/github/astrometry.net_win/util/simplexy.c.i

E_/github/astrometry.net_win/util/simplexy.s: E_/github/astrometry.net_win/util/simplexy.c.s
.PHONY : E_/github/astrometry.net_win/util/simplexy.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/simplexy.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.s
.PHONY : E_/github/astrometry.net_win/util/simplexy.c.s

E_/github/astrometry.net_win/util/sip-utils.obj: E_/github/astrometry.net_win/util/sip-utils.c.obj
.PHONY : E_/github/astrometry.net_win/util/sip-utils.obj

# target to build an object file
E_/github/astrometry.net_win/util/sip-utils.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj
.PHONY : E_/github/astrometry.net_win/util/sip-utils.c.obj

E_/github/astrometry.net_win/util/sip-utils.i: E_/github/astrometry.net_win/util/sip-utils.c.i
.PHONY : E_/github/astrometry.net_win/util/sip-utils.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/sip-utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.i
.PHONY : E_/github/astrometry.net_win/util/sip-utils.c.i

E_/github/astrometry.net_win/util/sip-utils.s: E_/github/astrometry.net_win/util/sip-utils.c.s
.PHONY : E_/github/astrometry.net_win/util/sip-utils.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/sip-utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.s
.PHONY : E_/github/astrometry.net_win/util/sip-utils.c.s

E_/github/astrometry.net_win/util/sip.obj: E_/github/astrometry.net_win/util/sip.c.obj
.PHONY : E_/github/astrometry.net_win/util/sip.obj

# target to build an object file
E_/github/astrometry.net_win/util/sip.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj
.PHONY : E_/github/astrometry.net_win/util/sip.c.obj

E_/github/astrometry.net_win/util/sip.i: E_/github/astrometry.net_win/util/sip.c.i
.PHONY : E_/github/astrometry.net_win/util/sip.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/sip.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.i
.PHONY : E_/github/astrometry.net_win/util/sip.c.i

E_/github/astrometry.net_win/util/sip.s: E_/github/astrometry.net_win/util/sip.c.s
.PHONY : E_/github/astrometry.net_win/util/sip.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/sip.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.s
.PHONY : E_/github/astrometry.net_win/util/sip.c.s

E_/github/astrometry.net_win/util/sip_qfits.obj: E_/github/astrometry.net_win/util/sip_qfits.c.obj
.PHONY : E_/github/astrometry.net_win/util/sip_qfits.obj

# target to build an object file
E_/github/astrometry.net_win/util/sip_qfits.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj
.PHONY : E_/github/astrometry.net_win/util/sip_qfits.c.obj

E_/github/astrometry.net_win/util/sip_qfits.i: E_/github/astrometry.net_win/util/sip_qfits.c.i
.PHONY : E_/github/astrometry.net_win/util/sip_qfits.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/sip_qfits.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.i
.PHONY : E_/github/astrometry.net_win/util/sip_qfits.c.i

E_/github/astrometry.net_win/util/sip_qfits.s: E_/github/astrometry.net_win/util/sip_qfits.c.s
.PHONY : E_/github/astrometry.net_win/util/sip_qfits.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/sip_qfits.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.s
.PHONY : E_/github/astrometry.net_win/util/sip_qfits.c.s

E_/github/astrometry.net_win/util/starkd.obj: E_/github/astrometry.net_win/util/starkd.c.obj
.PHONY : E_/github/astrometry.net_win/util/starkd.obj

# target to build an object file
E_/github/astrometry.net_win/util/starkd.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj
.PHONY : E_/github/astrometry.net_win/util/starkd.c.obj

E_/github/astrometry.net_win/util/starkd.i: E_/github/astrometry.net_win/util/starkd.c.i
.PHONY : E_/github/astrometry.net_win/util/starkd.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/starkd.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.i
.PHONY : E_/github/astrometry.net_win/util/starkd.c.i

E_/github/astrometry.net_win/util/starkd.s: E_/github/astrometry.net_win/util/starkd.c.s
.PHONY : E_/github/astrometry.net_win/util/starkd.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/starkd.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.s
.PHONY : E_/github/astrometry.net_win/util/starkd.c.s

E_/github/astrometry.net_win/util/starutil.obj: E_/github/astrometry.net_win/util/starutil.c.obj
.PHONY : E_/github/astrometry.net_win/util/starutil.obj

# target to build an object file
E_/github/astrometry.net_win/util/starutil.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj
.PHONY : E_/github/astrometry.net_win/util/starutil.c.obj

E_/github/astrometry.net_win/util/starutil.i: E_/github/astrometry.net_win/util/starutil.c.i
.PHONY : E_/github/astrometry.net_win/util/starutil.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/starutil.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.i
.PHONY : E_/github/astrometry.net_win/util/starutil.c.i

E_/github/astrometry.net_win/util/starutil.s: E_/github/astrometry.net_win/util/starutil.c.s
.PHONY : E_/github/astrometry.net_win/util/starutil.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/starutil.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.s
.PHONY : E_/github/astrometry.net_win/util/starutil.c.s

E_/github/astrometry.net_win/util/starxy.obj: E_/github/astrometry.net_win/util/starxy.c.obj
.PHONY : E_/github/astrometry.net_win/util/starxy.obj

# target to build an object file
E_/github/astrometry.net_win/util/starxy.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj
.PHONY : E_/github/astrometry.net_win/util/starxy.c.obj

E_/github/astrometry.net_win/util/starxy.i: E_/github/astrometry.net_win/util/starxy.c.i
.PHONY : E_/github/astrometry.net_win/util/starxy.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/starxy.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.i
.PHONY : E_/github/astrometry.net_win/util/starxy.c.i

E_/github/astrometry.net_win/util/starxy.s: E_/github/astrometry.net_win/util/starxy.c.s
.PHONY : E_/github/astrometry.net_win/util/starxy.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/starxy.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.s
.PHONY : E_/github/astrometry.net_win/util/starxy.c.s

E_/github/astrometry.net_win/util/tabsort.obj: E_/github/astrometry.net_win/util/tabsort.c.obj
.PHONY : E_/github/astrometry.net_win/util/tabsort.obj

# target to build an object file
E_/github/astrometry.net_win/util/tabsort.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj
.PHONY : E_/github/astrometry.net_win/util/tabsort.c.obj

E_/github/astrometry.net_win/util/tabsort.i: E_/github/astrometry.net_win/util/tabsort.c.i
.PHONY : E_/github/astrometry.net_win/util/tabsort.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/tabsort.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.i
.PHONY : E_/github/astrometry.net_win/util/tabsort.c.i

E_/github/astrometry.net_win/util/tabsort.s: E_/github/astrometry.net_win/util/tabsort.c.s
.PHONY : E_/github/astrometry.net_win/util/tabsort.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/tabsort.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.s
.PHONY : E_/github/astrometry.net_win/util/tabsort.c.s

E_/github/astrometry.net_win/util/tic.obj: E_/github/astrometry.net_win/util/tic.c.obj
.PHONY : E_/github/astrometry.net_win/util/tic.obj

# target to build an object file
E_/github/astrometry.net_win/util/tic.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj
.PHONY : E_/github/astrometry.net_win/util/tic.c.obj

E_/github/astrometry.net_win/util/tic.i: E_/github/astrometry.net_win/util/tic.c.i
.PHONY : E_/github/astrometry.net_win/util/tic.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/tic.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.i
.PHONY : E_/github/astrometry.net_win/util/tic.c.i

E_/github/astrometry.net_win/util/tic.s: E_/github/astrometry.net_win/util/tic.c.s
.PHONY : E_/github/astrometry.net_win/util/tic.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/tic.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.s
.PHONY : E_/github/astrometry.net_win/util/tic.c.s

E_/github/astrometry.net_win/util/wcs-rd2xy.obj: E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj
.PHONY : E_/github/astrometry.net_win/util/wcs-rd2xy.obj

# target to build an object file
E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj
.PHONY : E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj

E_/github/astrometry.net_win/util/wcs-rd2xy.i: E_/github/astrometry.net_win/util/wcs-rd2xy.c.i
.PHONY : E_/github/astrometry.net_win/util/wcs-rd2xy.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/wcs-rd2xy.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.i
.PHONY : E_/github/astrometry.net_win/util/wcs-rd2xy.c.i

E_/github/astrometry.net_win/util/wcs-rd2xy.s: E_/github/astrometry.net_win/util/wcs-rd2xy.c.s
.PHONY : E_/github/astrometry.net_win/util/wcs-rd2xy.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/wcs-rd2xy.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.s
.PHONY : E_/github/astrometry.net_win/util/wcs-rd2xy.c.s

E_/github/astrometry.net_win/util/wcs-resample.obj: E_/github/astrometry.net_win/util/wcs-resample.c.obj
.PHONY : E_/github/astrometry.net_win/util/wcs-resample.obj

# target to build an object file
E_/github/astrometry.net_win/util/wcs-resample.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-resample.c.obj
.PHONY : E_/github/astrometry.net_win/util/wcs-resample.c.obj

E_/github/astrometry.net_win/util/wcs-resample.i: E_/github/astrometry.net_win/util/wcs-resample.c.i
.PHONY : E_/github/astrometry.net_win/util/wcs-resample.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/wcs-resample.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-resample.c.i
.PHONY : E_/github/astrometry.net_win/util/wcs-resample.c.i

E_/github/astrometry.net_win/util/wcs-resample.s: E_/github/astrometry.net_win/util/wcs-resample.c.s
.PHONY : E_/github/astrometry.net_win/util/wcs-resample.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/wcs-resample.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-resample.c.s
.PHONY : E_/github/astrometry.net_win/util/wcs-resample.c.s

E_/github/astrometry.net_win/util/xylist.obj: E_/github/astrometry.net_win/util/xylist.c.obj
.PHONY : E_/github/astrometry.net_win/util/xylist.obj

# target to build an object file
E_/github/astrometry.net_win/util/xylist.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj
.PHONY : E_/github/astrometry.net_win/util/xylist.c.obj

E_/github/astrometry.net_win/util/xylist.i: E_/github/astrometry.net_win/util/xylist.c.i
.PHONY : E_/github/astrometry.net_win/util/xylist.i

# target to preprocess a source file
E_/github/astrometry.net_win/util/xylist.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.i
.PHONY : E_/github/astrometry.net_win/util/xylist.c.i

E_/github/astrometry.net_win/util/xylist.s: E_/github/astrometry.net_win/util/xylist.c.s
.PHONY : E_/github/astrometry.net_win/util/xylist.s

# target to generate assembly for a file
E_/github/astrometry.net_win/util/xylist.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.s
.PHONY : E_/github/astrometry.net_win/util/xylist.c.s

debug_cfitsio.obj: debug_cfitsio.c.obj
.PHONY : debug_cfitsio.obj

# target to build an object file
debug_cfitsio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/debug_cfitsio.c.obj
.PHONY : debug_cfitsio.c.obj

debug_cfitsio.i: debug_cfitsio.c.i
.PHONY : debug_cfitsio.i

# target to preprocess a source file
debug_cfitsio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/debug_cfitsio.c.i
.PHONY : debug_cfitsio.c.i

debug_cfitsio.s: debug_cfitsio.c.s
.PHONY : debug_cfitsio.s

# target to generate assembly for a file
debug_cfitsio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/debug_cfitsio.c.s
.PHONY : debug_cfitsio.c.s

debug_fits.obj: debug_fits.c.obj
.PHONY : debug_fits.obj

# target to build an object file
debug_fits.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/debug_fits.c.obj
.PHONY : debug_fits.c.obj

debug_fits.i: debug_fits.c.i
.PHONY : debug_fits.i

# target to preprocess a source file
debug_fits.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/debug_fits.c.i
.PHONY : debug_fits.c.i

debug_fits.s: debug_fits.c.s
.PHONY : debug_fits.s

# target to generate assembly for a file
debug_fits.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/debug_fits.c.s
.PHONY : debug_fits.c.s

debug_qfits_detailed.obj: debug_qfits_detailed.c.obj
.PHONY : debug_qfits_detailed.obj

# target to build an object file
debug_qfits_detailed.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj
.PHONY : debug_qfits_detailed.c.obj

debug_qfits_detailed.i: debug_qfits_detailed.c.i
.PHONY : debug_qfits_detailed.i

# target to preprocess a source file
debug_qfits_detailed.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.i
.PHONY : debug_qfits_detailed.c.i

debug_qfits_detailed.s: debug_qfits_detailed.c.s
.PHONY : debug_qfits_detailed.s

# target to generate assembly for a file
debug_qfits_detailed.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.s
.PHONY : debug_qfits_detailed.c.s

image2pnm.obj: image2pnm.c.obj
.PHONY : image2pnm.obj

# target to build an object file
image2pnm.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/image2pnm.c.obj
.PHONY : image2pnm.c.obj

image2pnm.i: image2pnm.c.i
.PHONY : image2pnm.i

# target to preprocess a source file
image2pnm.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/image2pnm.c.i
.PHONY : image2pnm.c.i

image2pnm.s: image2pnm.c.s
.PHONY : image2pnm.s

# target to generate assembly for a file
image2pnm.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/image2pnm.c.s
.PHONY : image2pnm.c.s

mman.obj: mman.c.obj
.PHONY : mman.obj

# target to build an object file
mman.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/mman.c.obj
.PHONY : mman.c.obj

mman.i: mman.c.i
.PHONY : mman.i

# target to preprocess a source file
mman.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/mman.c.i
.PHONY : mman.c.i

mman.s: mman.c.s
.PHONY : mman.s

# target to generate assembly for a file
mman.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/mman.c.s
.PHONY : mman.c.s

removelines.obj: removelines.c.obj
.PHONY : removelines.obj

# target to build an object file
removelines.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/removelines.c.obj
.PHONY : removelines.c.obj

removelines.i: removelines.c.i
.PHONY : removelines.i

# target to preprocess a source file
removelines.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/removelines.c.i
.PHONY : removelines.c.i

removelines.s: removelines.c.s
.PHONY : removelines.s

# target to generate assembly for a file
removelines.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/removelines.c.s
.PHONY : removelines.c.s

test-find-executable.obj: test-find-executable.c.obj
.PHONY : test-find-executable.obj

# target to build an object file
test-find-executable.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/test-find-executable.c.obj
.PHONY : test-find-executable.c.obj

test-find-executable.i: test-find-executable.c.i
.PHONY : test-find-executable.i

# target to preprocess a source file
test-find-executable.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/test-find-executable.c.i
.PHONY : test-find-executable.c.i

test-find-executable.s: test-find-executable.c.s
.PHONY : test-find-executable.s

# target to generate assembly for a file
test-find-executable.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/test-find-executable.c.s
.PHONY : test-find-executable.c.s

test_fits_read.obj: test_fits_read.c.obj
.PHONY : test_fits_read.obj

# target to build an object file
test_fits_read.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/test_fits_read.c.obj
.PHONY : test_fits_read.c.obj

test_fits_read.i: test_fits_read.c.i
.PHONY : test_fits_read.i

# target to preprocess a source file
test_fits_read.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/test_fits_read.c.i
.PHONY : test_fits_read.c.i

test_fits_read.s: test_fits_read.c.s
.PHONY : test_fits_read.s

# target to generate assembly for a file
test_fits_read.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/test_fits_read.c.s
.PHONY : test_fits_read.c.s

uniformize.obj: uniformize.c.obj
.PHONY : uniformize.obj

# target to build an object file
uniformize.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/uniformize.c.obj
.PHONY : uniformize.c.obj

uniformize.i: uniformize.c.i
.PHONY : uniformize.i

# target to preprocess a source file
uniformize.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/uniformize.c.i
.PHONY : uniformize.c.i

uniformize.s: uniformize.c.s
.PHONY : uniformize.s

# target to generate assembly for a file
uniformize.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/uniformize.c.s
.PHONY : uniformize.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... copy_dlls"
	@echo "... an-fitstopnm"
	@echo "... an-pnmtofits"
	@echo "... anbase"
	@echo "... anfiles"
	@echo "... anutils"
	@echo "... astrometry"
	@echo "... astrometry-engine"
	@echo "... catalogs"
	@echo "... debug_cfitsio"
	@echo "... debug_fits"
	@echo "... debug_qfits_detailed"
	@echo "... image2pnm"
	@echo "... libkd"
	@echo "... qfits"
	@echo "... removelines"
	@echo "... solve-field"
	@echo "... test-find-executable"
	@echo "... test_fits_read"
	@echo "... uniformize"
	@echo "... E_/github/astrometry.net_win/catalogs/2mass.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/2mass.i"
	@echo "... E_/github/astrometry.net_win/catalogs/2mass.s"
	@echo "... E_/github/astrometry.net_win/catalogs/brightstars.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/brightstars.i"
	@echo "... E_/github/astrometry.net_win/catalogs/brightstars.s"
	@echo "... E_/github/astrometry.net_win/catalogs/constellation-boundaries.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/constellation-boundaries.i"
	@echo "... E_/github/astrometry.net_win/catalogs/constellation-boundaries.s"
	@echo "... E_/github/astrometry.net_win/catalogs/constellations.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/constellations.i"
	@echo "... E_/github/astrometry.net_win/catalogs/constellations.s"
	@echo "... E_/github/astrometry.net_win/catalogs/hd.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/hd.i"
	@echo "... E_/github/astrometry.net_win/catalogs/hd.s"
	@echo "... E_/github/astrometry.net_win/catalogs/nomad.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/nomad.i"
	@echo "... E_/github/astrometry.net_win/catalogs/nomad.s"
	@echo "... E_/github/astrometry.net_win/catalogs/openngc.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/openngc.i"
	@echo "... E_/github/astrometry.net_win/catalogs/openngc.s"
	@echo "... E_/github/astrometry.net_win/catalogs/stellarium-constellations.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/stellarium-constellations.i"
	@echo "... E_/github/astrometry.net_win/catalogs/stellarium-constellations.s"
	@echo "... E_/github/astrometry.net_win/catalogs/tycho2.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/tycho2.i"
	@echo "... E_/github/astrometry.net_win/catalogs/tycho2.s"
	@echo "... E_/github/astrometry.net_win/catalogs/ucac3.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/ucac3.i"
	@echo "... E_/github/astrometry.net_win/catalogs/ucac3.s"
	@echo "... E_/github/astrometry.net_win/catalogs/ucac4.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/ucac4.i"
	@echo "... E_/github/astrometry.net_win/catalogs/ucac4.s"
	@echo "... E_/github/astrometry.net_win/catalogs/usnob.obj"
	@echo "... E_/github/astrometry.net_win/catalogs/usnob.i"
	@echo "... E_/github/astrometry.net_win/catalogs/usnob.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_ddd.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_ddd.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_ddd.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_dds.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_dds.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_dds.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_ddu.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_ddu.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_ddu.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_dss.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_dss.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_dss.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_duu.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_duu.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_duu.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_fff.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_fff.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_fff.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_lll.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_lll.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdint_lll.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree_dim.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree_dim.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree_dim.s"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree_fits_io.obj"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree_fits_io.i"
	@echo "... E_/github/astrometry.net_win/libkd/kdtree_fits_io.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/anqfits.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/anqfits.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/anqfits.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/md5.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/md5.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/md5.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_byteswap.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_byteswap.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_byteswap.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_card.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_card.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_card.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_convert.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_convert.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_convert.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_error.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_error.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_error.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_float.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_float.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_float.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_header.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_header.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_header.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_image.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_image.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_image.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_md5.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_md5.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_md5.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_memory.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_memory.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_memory.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_rw.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_rw.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_rw.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_table.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_table.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_table.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_time.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_time.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_time.s"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_tools.obj"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_tools.i"
	@echo "... E_/github/astrometry.net_win/qfits-an/qfits_tools.s"
	@echo "... E_/github/astrometry.net_win/solver/augment-xylist.obj"
	@echo "... E_/github/astrometry.net_win/solver/augment-xylist.i"
	@echo "... E_/github/astrometry.net_win/solver/augment-xylist.s"
	@echo "... E_/github/astrometry.net_win/solver/build-index.obj"
	@echo "... E_/github/astrometry.net_win/solver/build-index.i"
	@echo "... E_/github/astrometry.net_win/solver/build-index.s"
	@echo "... E_/github/astrometry.net_win/solver/codefile.obj"
	@echo "... E_/github/astrometry.net_win/solver/codefile.i"
	@echo "... E_/github/astrometry.net_win/solver/codefile.s"
	@echo "... E_/github/astrometry.net_win/solver/codetree.obj"
	@echo "... E_/github/astrometry.net_win/solver/codetree.i"
	@echo "... E_/github/astrometry.net_win/solver/codetree.s"
	@echo "... E_/github/astrometry.net_win/solver/cut-table.obj"
	@echo "... E_/github/astrometry.net_win/solver/cut-table.i"
	@echo "... E_/github/astrometry.net_win/solver/cut-table.s"
	@echo "... E_/github/astrometry.net_win/solver/engine-main.obj"
	@echo "... E_/github/astrometry.net_win/solver/engine-main.i"
	@echo "... E_/github/astrometry.net_win/solver/engine-main.s"
	@echo "... E_/github/astrometry.net_win/solver/engine.obj"
	@echo "... E_/github/astrometry.net_win/solver/engine.i"
	@echo "... E_/github/astrometry.net_win/solver/engine.s"
	@echo "... E_/github/astrometry.net_win/solver/fits-guess-scale.obj"
	@echo "... E_/github/astrometry.net_win/solver/fits-guess-scale.i"
	@echo "... E_/github/astrometry.net_win/solver/fits-guess-scale.s"
	@echo "... E_/github/astrometry.net_win/solver/hpquads.obj"
	@echo "... E_/github/astrometry.net_win/solver/hpquads.i"
	@echo "... E_/github/astrometry.net_win/solver/hpquads.s"
	@echo "... E_/github/astrometry.net_win/solver/image2xy-files.obj"
	@echo "... E_/github/astrometry.net_win/solver/image2xy-files.i"
	@echo "... E_/github/astrometry.net_win/solver/image2xy-files.s"
	@echo "... E_/github/astrometry.net_win/solver/merge-index.obj"
	@echo "... E_/github/astrometry.net_win/solver/merge-index.i"
	@echo "... E_/github/astrometry.net_win/solver/merge-index.s"
	@echo "... E_/github/astrometry.net_win/solver/new-wcs.obj"
	@echo "... E_/github/astrometry.net_win/solver/new-wcs.i"
	@echo "... E_/github/astrometry.net_win/solver/new-wcs.s"
	@echo "... E_/github/astrometry.net_win/solver/onefield.obj"
	@echo "... E_/github/astrometry.net_win/solver/onefield.i"
	@echo "... E_/github/astrometry.net_win/solver/onefield.s"
	@echo "... E_/github/astrometry.net_win/solver/quad-builder.obj"
	@echo "... E_/github/astrometry.net_win/solver/quad-builder.i"
	@echo "... E_/github/astrometry.net_win/solver/quad-builder.s"
	@echo "... E_/github/astrometry.net_win/solver/quad-utils.obj"
	@echo "... E_/github/astrometry.net_win/solver/quad-utils.i"
	@echo "... E_/github/astrometry.net_win/solver/quad-utils.s"
	@echo "... E_/github/astrometry.net_win/solver/resort-xylist.obj"
	@echo "... E_/github/astrometry.net_win/solver/resort-xylist.i"
	@echo "... E_/github/astrometry.net_win/solver/resort-xylist.s"
	@echo "... E_/github/astrometry.net_win/solver/solve-field.obj"
	@echo "... E_/github/astrometry.net_win/solver/solve-field.i"
	@echo "... E_/github/astrometry.net_win/solver/solve-field.s"
	@echo "... E_/github/astrometry.net_win/solver/solvedfile.obj"
	@echo "... E_/github/astrometry.net_win/solver/solvedfile.i"
	@echo "... E_/github/astrometry.net_win/solver/solvedfile.s"
	@echo "... E_/github/astrometry.net_win/solver/solver.obj"
	@echo "... E_/github/astrometry.net_win/solver/solver.i"
	@echo "... E_/github/astrometry.net_win/solver/solver.s"
	@echo "... E_/github/astrometry.net_win/solver/solverutils.obj"
	@echo "... E_/github/astrometry.net_win/solver/solverutils.i"
	@echo "... E_/github/astrometry.net_win/solver/solverutils.s"
	@echo "... E_/github/astrometry.net_win/solver/startree.obj"
	@echo "... E_/github/astrometry.net_win/solver/startree.i"
	@echo "... E_/github/astrometry.net_win/solver/startree.s"
	@echo "... E_/github/astrometry.net_win/solver/tweak.obj"
	@echo "... E_/github/astrometry.net_win/solver/tweak.i"
	@echo "... E_/github/astrometry.net_win/solver/tweak.s"
	@echo "... E_/github/astrometry.net_win/solver/tweak2.obj"
	@echo "... E_/github/astrometry.net_win/solver/tweak2.i"
	@echo "... E_/github/astrometry.net_win/solver/tweak2.s"
	@echo "... E_/github/astrometry.net_win/solver/uniformize-catalog.obj"
	@echo "... E_/github/astrometry.net_win/solver/uniformize-catalog.i"
	@echo "... E_/github/astrometry.net_win/solver/uniformize-catalog.s"
	@echo "... E_/github/astrometry.net_win/solver/unpermute-quads.obj"
	@echo "... E_/github/astrometry.net_win/solver/unpermute-quads.i"
	@echo "... E_/github/astrometry.net_win/solver/unpermute-quads.s"
	@echo "... E_/github/astrometry.net_win/solver/unpermute-stars.obj"
	@echo "... E_/github/astrometry.net_win/solver/unpermute-stars.i"
	@echo "... E_/github/astrometry.net_win/solver/unpermute-stars.s"
	@echo "... E_/github/astrometry.net_win/solver/verify.obj"
	@echo "... E_/github/astrometry.net_win/solver/verify.i"
	@echo "... E_/github/astrometry.net_win/solver/verify.s"
	@echo "... E_/github/astrometry.net_win/util/an-endian.obj"
	@echo "... E_/github/astrometry.net_win/util/an-endian.i"
	@echo "... E_/github/astrometry.net_win/util/an-endian.s"
	@echo "... E_/github/astrometry.net_win/util/an-fitstopnm.obj"
	@echo "... E_/github/astrometry.net_win/util/an-fitstopnm.i"
	@echo "... E_/github/astrometry.net_win/util/an-fitstopnm.s"
	@echo "... E_/github/astrometry.net_win/util/an-opts.obj"
	@echo "... E_/github/astrometry.net_win/util/an-opts.i"
	@echo "... E_/github/astrometry.net_win/util/an-opts.s"
	@echo "... E_/github/astrometry.net_win/util/an-pnmtofits.obj"
	@echo "... E_/github/astrometry.net_win/util/an-pnmtofits.i"
	@echo "... E_/github/astrometry.net_win/util/an-pnmtofits.s"
	@echo "... E_/github/astrometry.net_win/util/anwcs.obj"
	@echo "... E_/github/astrometry.net_win/util/anwcs.i"
	@echo "... E_/github/astrometry.net_win/util/anwcs.s"
	@echo "... E_/github/astrometry.net_win/util/bl-sort.obj"
	@echo "... E_/github/astrometry.net_win/util/bl-sort.i"
	@echo "... E_/github/astrometry.net_win/util/bl-sort.s"
	@echo "... E_/github/astrometry.net_win/util/bl.obj"
	@echo "... E_/github/astrometry.net_win/util/bl.i"
	@echo "... E_/github/astrometry.net_win/util/bl.s"
	@echo "... E_/github/astrometry.net_win/util/codekd.obj"
	@echo "... E_/github/astrometry.net_win/util/codekd.i"
	@echo "... E_/github/astrometry.net_win/util/codekd.s"
	@echo "... E_/github/astrometry.net_win/util/ctmf.obj"
	@echo "... E_/github/astrometry.net_win/util/ctmf.i"
	@echo "... E_/github/astrometry.net_win/util/ctmf.s"
	@echo "... E_/github/astrometry.net_win/util/dallpeaks.obj"
	@echo "... E_/github/astrometry.net_win/util/dallpeaks.i"
	@echo "... E_/github/astrometry.net_win/util/dallpeaks.s"
	@echo "... E_/github/astrometry.net_win/util/datalog.obj"
	@echo "... E_/github/astrometry.net_win/util/datalog.i"
	@echo "... E_/github/astrometry.net_win/util/datalog.s"
	@echo "... E_/github/astrometry.net_win/util/dcen3x3.obj"
	@echo "... E_/github/astrometry.net_win/util/dcen3x3.i"
	@echo "... E_/github/astrometry.net_win/util/dcen3x3.s"
	@echo "... E_/github/astrometry.net_win/util/dfind.obj"
	@echo "... E_/github/astrometry.net_win/util/dfind.i"
	@echo "... E_/github/astrometry.net_win/util/dfind.s"
	@echo "... E_/github/astrometry.net_win/util/dmedsmooth.obj"
	@echo "... E_/github/astrometry.net_win/util/dmedsmooth.i"
	@echo "... E_/github/astrometry.net_win/util/dmedsmooth.s"
	@echo "... E_/github/astrometry.net_win/util/dobjects.obj"
	@echo "... E_/github/astrometry.net_win/util/dobjects.i"
	@echo "... E_/github/astrometry.net_win/util/dobjects.s"
	@echo "... E_/github/astrometry.net_win/util/dpeaks.obj"
	@echo "... E_/github/astrometry.net_win/util/dpeaks.i"
	@echo "... E_/github/astrometry.net_win/util/dpeaks.s"
	@echo "... E_/github/astrometry.net_win/util/dselip.obj"
	@echo "... E_/github/astrometry.net_win/util/dselip.i"
	@echo "... E_/github/astrometry.net_win/util/dselip.s"
	@echo "... E_/github/astrometry.net_win/util/dsigma.obj"
	@echo "... E_/github/astrometry.net_win/util/dsigma.i"
	@echo "... E_/github/astrometry.net_win/util/dsigma.s"
	@echo "... E_/github/astrometry.net_win/util/dsmooth.obj"
	@echo "... E_/github/astrometry.net_win/util/dsmooth.i"
	@echo "... E_/github/astrometry.net_win/util/dsmooth.s"
	@echo "... E_/github/astrometry.net_win/util/errors.obj"
	@echo "... E_/github/astrometry.net_win/util/errors.i"
	@echo "... E_/github/astrometry.net_win/util/errors.s"
	@echo "... E_/github/astrometry.net_win/util/fileutils.obj"
	@echo "... E_/github/astrometry.net_win/util/fileutils.i"
	@echo "... E_/github/astrometry.net_win/util/fileutils.s"
	@echo "... E_/github/astrometry.net_win/util/fit-wcs.obj"
	@echo "... E_/github/astrometry.net_win/util/fit-wcs.i"
	@echo "... E_/github/astrometry.net_win/util/fit-wcs.s"
	@echo "... E_/github/astrometry.net_win/util/fitsbin.obj"
	@echo "... E_/github/astrometry.net_win/util/fitsbin.i"
	@echo "... E_/github/astrometry.net_win/util/fitsbin.s"
	@echo "... E_/github/astrometry.net_win/util/fitsfile.obj"
	@echo "... E_/github/astrometry.net_win/util/fitsfile.i"
	@echo "... E_/github/astrometry.net_win/util/fitsfile.s"
	@echo "... E_/github/astrometry.net_win/util/fitsioutils.obj"
	@echo "... E_/github/astrometry.net_win/util/fitsioutils.i"
	@echo "... E_/github/astrometry.net_win/util/fitsioutils.s"
	@echo "... E_/github/astrometry.net_win/util/fitstable.obj"
	@echo "... E_/github/astrometry.net_win/util/fitstable.i"
	@echo "... E_/github/astrometry.net_win/util/fitstable.s"
	@echo "... E_/github/astrometry.net_win/util/gslutils.obj"
	@echo "... E_/github/astrometry.net_win/util/gslutils.i"
	@echo "... E_/github/astrometry.net_win/util/gslutils.s"
	@echo "... E_/github/astrometry.net_win/util/healpix-utils.obj"
	@echo "... E_/github/astrometry.net_win/util/healpix-utils.i"
	@echo "... E_/github/astrometry.net_win/util/healpix-utils.s"
	@echo "... E_/github/astrometry.net_win/util/healpix.obj"
	@echo "... E_/github/astrometry.net_win/util/healpix.i"
	@echo "... E_/github/astrometry.net_win/util/healpix.s"
	@echo "... E_/github/astrometry.net_win/util/image2xy.obj"
	@echo "... E_/github/astrometry.net_win/util/image2xy.i"
	@echo "... E_/github/astrometry.net_win/util/image2xy.s"
	@echo "... E_/github/astrometry.net_win/util/index.obj"
	@echo "... E_/github/astrometry.net_win/util/index.i"
	@echo "... E_/github/astrometry.net_win/util/index.s"
	@echo "... E_/github/astrometry.net_win/util/indexset.obj"
	@echo "... E_/github/astrometry.net_win/util/indexset.i"
	@echo "... E_/github/astrometry.net_win/util/indexset.s"
	@echo "... E_/github/astrometry.net_win/util/ioutils.obj"
	@echo "... E_/github/astrometry.net_win/util/ioutils.i"
	@echo "... E_/github/astrometry.net_win/util/ioutils.s"
	@echo "... E_/github/astrometry.net_win/util/log.obj"
	@echo "... E_/github/astrometry.net_win/util/log.i"
	@echo "... E_/github/astrometry.net_win/util/log.s"
	@echo "... E_/github/astrometry.net_win/util/matchfile.obj"
	@echo "... E_/github/astrometry.net_win/util/matchfile.i"
	@echo "... E_/github/astrometry.net_win/util/matchfile.s"
	@echo "... E_/github/astrometry.net_win/util/matchobj.obj"
	@echo "... E_/github/astrometry.net_win/util/matchobj.i"
	@echo "... E_/github/astrometry.net_win/util/matchobj.s"
	@echo "... E_/github/astrometry.net_win/util/mathutil.obj"
	@echo "... E_/github/astrometry.net_win/util/mathutil.i"
	@echo "... E_/github/astrometry.net_win/util/mathutil.s"
	@echo "... E_/github/astrometry.net_win/util/multiindex.obj"
	@echo "... E_/github/astrometry.net_win/util/multiindex.i"
	@echo "... E_/github/astrometry.net_win/util/multiindex.s"
	@echo "... E_/github/astrometry.net_win/util/permutedsort.obj"
	@echo "... E_/github/astrometry.net_win/util/permutedsort.i"
	@echo "... E_/github/astrometry.net_win/util/permutedsort.s"
	@echo "... E_/github/astrometry.net_win/util/qidxfile.obj"
	@echo "... E_/github/astrometry.net_win/util/qidxfile.i"
	@echo "... E_/github/astrometry.net_win/util/qidxfile.s"
	@echo "... E_/github/astrometry.net_win/util/quadfile.obj"
	@echo "... E_/github/astrometry.net_win/util/quadfile.i"
	@echo "... E_/github/astrometry.net_win/util/quadfile.s"
	@echo "... E_/github/astrometry.net_win/util/rdlist.obj"
	@echo "... E_/github/astrometry.net_win/util/rdlist.i"
	@echo "... E_/github/astrometry.net_win/util/rdlist.s"
	@echo "... E_/github/astrometry.net_win/util/resample.obj"
	@echo "... E_/github/astrometry.net_win/util/resample.i"
	@echo "... E_/github/astrometry.net_win/util/resample.s"
	@echo "... E_/github/astrometry.net_win/util/scamp-catalog.obj"
	@echo "... E_/github/astrometry.net_win/util/scamp-catalog.i"
	@echo "... E_/github/astrometry.net_win/util/scamp-catalog.s"
	@echo "... E_/github/astrometry.net_win/util/scamp.obj"
	@echo "... E_/github/astrometry.net_win/util/scamp.i"
	@echo "... E_/github/astrometry.net_win/util/scamp.s"
	@echo "... E_/github/astrometry.net_win/util/simplexy.obj"
	@echo "... E_/github/astrometry.net_win/util/simplexy.i"
	@echo "... E_/github/astrometry.net_win/util/simplexy.s"
	@echo "... E_/github/astrometry.net_win/util/sip-utils.obj"
	@echo "... E_/github/astrometry.net_win/util/sip-utils.i"
	@echo "... E_/github/astrometry.net_win/util/sip-utils.s"
	@echo "... E_/github/astrometry.net_win/util/sip.obj"
	@echo "... E_/github/astrometry.net_win/util/sip.i"
	@echo "... E_/github/astrometry.net_win/util/sip.s"
	@echo "... E_/github/astrometry.net_win/util/sip_qfits.obj"
	@echo "... E_/github/astrometry.net_win/util/sip_qfits.i"
	@echo "... E_/github/astrometry.net_win/util/sip_qfits.s"
	@echo "... E_/github/astrometry.net_win/util/starkd.obj"
	@echo "... E_/github/astrometry.net_win/util/starkd.i"
	@echo "... E_/github/astrometry.net_win/util/starkd.s"
	@echo "... E_/github/astrometry.net_win/util/starutil.obj"
	@echo "... E_/github/astrometry.net_win/util/starutil.i"
	@echo "... E_/github/astrometry.net_win/util/starutil.s"
	@echo "... E_/github/astrometry.net_win/util/starxy.obj"
	@echo "... E_/github/astrometry.net_win/util/starxy.i"
	@echo "... E_/github/astrometry.net_win/util/starxy.s"
	@echo "... E_/github/astrometry.net_win/util/tabsort.obj"
	@echo "... E_/github/astrometry.net_win/util/tabsort.i"
	@echo "... E_/github/astrometry.net_win/util/tabsort.s"
	@echo "... E_/github/astrometry.net_win/util/tic.obj"
	@echo "... E_/github/astrometry.net_win/util/tic.i"
	@echo "... E_/github/astrometry.net_win/util/tic.s"
	@echo "... E_/github/astrometry.net_win/util/wcs-rd2xy.obj"
	@echo "... E_/github/astrometry.net_win/util/wcs-rd2xy.i"
	@echo "... E_/github/astrometry.net_win/util/wcs-rd2xy.s"
	@echo "... E_/github/astrometry.net_win/util/wcs-resample.obj"
	@echo "... E_/github/astrometry.net_win/util/wcs-resample.i"
	@echo "... E_/github/astrometry.net_win/util/wcs-resample.s"
	@echo "... E_/github/astrometry.net_win/util/xylist.obj"
	@echo "... E_/github/astrometry.net_win/util/xylist.i"
	@echo "... E_/github/astrometry.net_win/util/xylist.s"
	@echo "... debug_cfitsio.obj"
	@echo "... debug_cfitsio.i"
	@echo "... debug_cfitsio.s"
	@echo "... debug_fits.obj"
	@echo "... debug_fits.i"
	@echo "... debug_fits.s"
	@echo "... debug_qfits_detailed.obj"
	@echo "... debug_qfits_detailed.i"
	@echo "... debug_qfits_detailed.s"
	@echo "... image2pnm.obj"
	@echo "... image2pnm.i"
	@echo "... image2pnm.s"
	@echo "... mman.obj"
	@echo "... mman.i"
	@echo "... mman.s"
	@echo "... removelines.obj"
	@echo "... removelines.i"
	@echo "... removelines.s"
	@echo "... test-find-executable.obj"
	@echo "... test-find-executable.i"
	@echo "... test-find-executable.s"
	@echo "... test_fits_read.obj"
	@echo "... test_fits_read.i"
	@echo "... test_fits_read.s"
	@echo "... uniformize.obj"
	@echo "... uniformize.i"
	@echo "... uniformize.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

