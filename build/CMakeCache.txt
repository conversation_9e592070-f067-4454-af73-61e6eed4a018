# This is the CMakeCache file.
# For build in directory: e:/github/astrometry.net_win/build
# It was generated by CMake: C:/msys64/mingw64/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=C:/msys64/mingw64/bin/addr2line.exe

//Path to a program.
CMAKE_AR:FILEPATH=C:/msys64/mingw64/bin/ar.exe

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//C compiler
CMAKE_C_COMPILER:FILEPATH=C:/msys64/mingw64/bin/cc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=C:/msys64/mingw64/bin/gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=C:/msys64/mingw64/bin/gcc-ranlib.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=C:/msys64/mingw64/bin/dlltool.exe

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=E:/github/astrometry.net_win/build/CMakeFiles/pkgRedirects

//Convert GNU import libraries to MS format (requires Visual Studio)
CMAKE_GNUtoMS:BOOL=OFF

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/astrometry_net

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/msys64/mingw64/bin/ld.exe

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/msys64/usr/bin/make.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=C:/msys64/mingw64/bin/nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/msys64/mingw64/bin/objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=C:/msys64/mingw64/bin/objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=astrometry_net

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=C:/msys64/mingw64/bin/ranlib.exe

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/msys64/mingw64/bin/windres.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_READELF:FILEPATH=C:/msys64/mingw64/bin/readelf.exe

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the archiver during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the archiver during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the archiver during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=C:/msys64/mingw64/bin/strip.exe

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=C:/msys64/mingw64/bin/pkg-config.exe

//Path to a library.
REGEX_LIBRARY:FILEPATH=C:/msys64/mingw64/lib/libregex.dll.a

//Value Computed by CMake
astrometry_net_BINARY_DIR:STATIC=E:/github/astrometry.net_win/build

//Value Computed by CMake
astrometry_net_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
astrometry_net_SOURCE_DIR:STATIC=E:/github/astrometry.net_win/win

//Path to a library.
pkgcfg_lib_CFITSIO_cfitsio:FILEPATH=C:/msys64/mingw64/lib/libcfitsio.dll.a

//Path to a library.
pkgcfg_lib_GSL_gsl:FILEPATH=C:/msys64/mingw64/lib/libgsl.dll.a

//Path to a library.
pkgcfg_lib_GSL_gslcblas:FILEPATH=C:/msys64/mingw64/lib/libgslcblas.dll.a

//Path to a library.
pkgcfg_lib_GSL_m:FILEPATH=C:/msys64/mingw64/lib/libm.a


########################
# INTERNAL cache entries
########################

CAIRO_CFLAGS:INTERNAL=
CAIRO_CFLAGS_I:INTERNAL=
CAIRO_CFLAGS_OTHER:INTERNAL=
CAIRO_FOUND:INTERNAL=
CAIRO_INCLUDEDIR:INTERNAL=
CAIRO_LIBDIR:INTERNAL=
CAIRO_LIBS:INTERNAL=
CAIRO_LIBS_L:INTERNAL=
CAIRO_LIBS_OTHER:INTERNAL=
CAIRO_LIBS_PATHS:INTERNAL=
CAIRO_MODULE_NAME:INTERNAL=
CAIRO_PREFIX:INTERNAL=
CAIRO_STATIC_CFLAGS:INTERNAL=
CAIRO_STATIC_CFLAGS_I:INTERNAL=
CAIRO_STATIC_CFLAGS_OTHER:INTERNAL=
CAIRO_STATIC_LIBDIR:INTERNAL=
CAIRO_STATIC_LIBS:INTERNAL=
CAIRO_STATIC_LIBS_L:INTERNAL=
CAIRO_STATIC_LIBS_OTHER:INTERNAL=
CAIRO_STATIC_LIBS_PATHS:INTERNAL=
CAIRO_VERSION:INTERNAL=
CAIRO_cairo_INCLUDEDIR:INTERNAL=
CAIRO_cairo_LIBDIR:INTERNAL=
CAIRO_cairo_PREFIX:INTERNAL=
CAIRO_cairo_VERSION:INTERNAL=
CFITSIO_CFLAGS:INTERNAL=-IC:/msys64/mingw64/include
CFITSIO_CFLAGS_I:INTERNAL=
CFITSIO_CFLAGS_OTHER:INTERNAL=
CFITSIO_FOUND:INTERNAL=1
CFITSIO_INCLUDEDIR:INTERNAL=C:/msys64/mingw64/include
CFITSIO_INCLUDE_DIRS:INTERNAL=C:/msys64/mingw64/include
CFITSIO_LDFLAGS:INTERNAL=-LC:/msys64/mingw64/lib;-lcfitsio
CFITSIO_LDFLAGS_OTHER:INTERNAL=
CFITSIO_LIBDIR:INTERNAL=C:/msys64/mingw64/lib
CFITSIO_LIBRARIES:INTERNAL=cfitsio
CFITSIO_LIBRARY_DIRS:INTERNAL=C:/msys64/mingw64/lib
CFITSIO_LIBS:INTERNAL=
CFITSIO_LIBS_L:INTERNAL=
CFITSIO_LIBS_OTHER:INTERNAL=
CFITSIO_LIBS_PATHS:INTERNAL=
CFITSIO_MODULE_NAME:INTERNAL=cfitsio
CFITSIO_PREFIX:INTERNAL=C:/msys64/mingw64
CFITSIO_STATIC_CFLAGS:INTERNAL=-IC:/msys64/mingw64/include;-DCFITSIO_STATIC_DEFINE
CFITSIO_STATIC_CFLAGS_I:INTERNAL=
CFITSIO_STATIC_CFLAGS_OTHER:INTERNAL=-DCFITSIO_STATIC_DEFINE
CFITSIO_STATIC_INCLUDE_DIRS:INTERNAL=C:/msys64/mingw64/include
CFITSIO_STATIC_LDFLAGS:INTERNAL=-LC:/msys64/mingw64/lib;-lcfitsio;-lz;-lcurl;-lm
CFITSIO_STATIC_LDFLAGS_OTHER:INTERNAL=
CFITSIO_STATIC_LIBDIR:INTERNAL=
CFITSIO_STATIC_LIBRARIES:INTERNAL=cfitsio;z;curl;m
CFITSIO_STATIC_LIBRARY_DIRS:INTERNAL=C:/msys64/mingw64/lib
CFITSIO_STATIC_LIBS:INTERNAL=
CFITSIO_STATIC_LIBS_L:INTERNAL=
CFITSIO_STATIC_LIBS_OTHER:INTERNAL=
CFITSIO_STATIC_LIBS_PATHS:INTERNAL=
CFITSIO_VERSION:INTERNAL=4.6.2
CFITSIO_cfitsio_INCLUDEDIR:INTERNAL=
CFITSIO_cfitsio_LIBDIR:INTERNAL=
CFITSIO_cfitsio_PREFIX:INTERNAL=
CFITSIO_cfitsio_VERSION:INTERNAL=
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=e:/github/astrometry.net_win/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/msys64/mingw64/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/msys64/mingw64/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/msys64/mingw64/bin/ctest.exe
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=MSYS Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=E:/github/astrometry.net_win/win
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/msys64/mingw64/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[C:/msys64/mingw64/bin/pkg-config.exe][v2.5.1()]
GSL_CFLAGS:INTERNAL=-IC:/msys64/mingw64/include
GSL_CFLAGS_I:INTERNAL=
GSL_CFLAGS_OTHER:INTERNAL=
GSL_FOUND:INTERNAL=1
GSL_INCLUDEDIR:INTERNAL=C:/msys64/mingw64/include
GSL_INCLUDE_DIRS:INTERNAL=C:/msys64/mingw64/include
GSL_LDFLAGS:INTERNAL=-LC:/msys64/mingw64/lib;-lgsl;-lgslcblas;-lm
GSL_LDFLAGS_OTHER:INTERNAL=
GSL_LIBDIR:INTERNAL=C:/msys64/mingw64/lib
GSL_LIBRARIES:INTERNAL=gsl;gslcblas;m
GSL_LIBRARY_DIRS:INTERNAL=C:/msys64/mingw64/lib
GSL_LIBS:INTERNAL=
GSL_LIBS_L:INTERNAL=
GSL_LIBS_OTHER:INTERNAL=
GSL_LIBS_PATHS:INTERNAL=
GSL_MODULE_NAME:INTERNAL=gsl
GSL_PREFIX:INTERNAL=C:/msys64/mingw64
GSL_STATIC_CFLAGS:INTERNAL=-IC:/msys64/mingw64/include
GSL_STATIC_CFLAGS_I:INTERNAL=
GSL_STATIC_CFLAGS_OTHER:INTERNAL=
GSL_STATIC_INCLUDE_DIRS:INTERNAL=C:/msys64/mingw64/include
GSL_STATIC_LDFLAGS:INTERNAL=-LC:/msys64/mingw64/lib;-lgsl;-lgslcblas;-lm
GSL_STATIC_LDFLAGS_OTHER:INTERNAL=
GSL_STATIC_LIBDIR:INTERNAL=
GSL_STATIC_LIBRARIES:INTERNAL=gsl;gslcblas;m
GSL_STATIC_LIBRARY_DIRS:INTERNAL=C:/msys64/mingw64/lib
GSL_STATIC_LIBS:INTERNAL=
GSL_STATIC_LIBS_L:INTERNAL=
GSL_STATIC_LIBS_OTHER:INTERNAL=
GSL_STATIC_LIBS_PATHS:INTERNAL=
GSL_VERSION:INTERNAL=2.8
GSL_gsl_INCLUDEDIR:INTERNAL=
GSL_gsl_LIBDIR:INTERNAL=
GSL_gsl_PREFIX:INTERNAL=
GSL_gsl_VERSION:INTERNAL=
JPEG_CFLAGS:INTERNAL=
JPEG_CFLAGS_I:INTERNAL=
JPEG_CFLAGS_OTHER:INTERNAL=
JPEG_FOUND:INTERNAL=
JPEG_INCLUDEDIR:INTERNAL=
JPEG_LIBDIR:INTERNAL=
JPEG_LIBS:INTERNAL=
JPEG_LIBS_L:INTERNAL=
JPEG_LIBS_OTHER:INTERNAL=
JPEG_LIBS_PATHS:INTERNAL=
JPEG_MODULE_NAME:INTERNAL=
JPEG_PREFIX:INTERNAL=
JPEG_STATIC_CFLAGS:INTERNAL=
JPEG_STATIC_CFLAGS_I:INTERNAL=
JPEG_STATIC_CFLAGS_OTHER:INTERNAL=
JPEG_STATIC_LIBDIR:INTERNAL=
JPEG_STATIC_LIBS:INTERNAL=
JPEG_STATIC_LIBS_L:INTERNAL=
JPEG_STATIC_LIBS_OTHER:INTERNAL=
JPEG_STATIC_LIBS_PATHS:INTERNAL=
JPEG_VERSION:INTERNAL=
JPEG_libjpeg_INCLUDEDIR:INTERNAL=
JPEG_libjpeg_LIBDIR:INTERNAL=
JPEG_libjpeg_PREFIX:INTERNAL=
JPEG_libjpeg_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
PNG_CFLAGS:INTERNAL=
PNG_CFLAGS_I:INTERNAL=
PNG_CFLAGS_OTHER:INTERNAL=
PNG_FOUND:INTERNAL=
PNG_INCLUDEDIR:INTERNAL=
PNG_LIBDIR:INTERNAL=
PNG_LIBS:INTERNAL=
PNG_LIBS_L:INTERNAL=
PNG_LIBS_OTHER:INTERNAL=
PNG_LIBS_PATHS:INTERNAL=
PNG_MODULE_NAME:INTERNAL=
PNG_PREFIX:INTERNAL=
PNG_STATIC_CFLAGS:INTERNAL=
PNG_STATIC_CFLAGS_I:INTERNAL=
PNG_STATIC_CFLAGS_OTHER:INTERNAL=
PNG_STATIC_LIBDIR:INTERNAL=
PNG_STATIC_LIBS:INTERNAL=
PNG_STATIC_LIBS_L:INTERNAL=
PNG_STATIC_LIBS_OTHER:INTERNAL=
PNG_STATIC_LIBS_PATHS:INTERNAL=
PNG_VERSION:INTERNAL=
PNG_libpng_INCLUDEDIR:INTERNAL=
PNG_libpng_LIBDIR:INTERNAL=
PNG_libpng_PREFIX:INTERNAL=
PNG_libpng_VERSION:INTERNAL=
__pkg_config_arguments_CFITSIO:INTERNAL=REQUIRED;cfitsio
__pkg_config_arguments_GSL:INTERNAL=gsl
__pkg_config_checked_CAIRO:INTERNAL=1
__pkg_config_checked_CFITSIO:INTERNAL=1
__pkg_config_checked_GSL:INTERNAL=1
__pkg_config_checked_JPEG:INTERNAL=1
__pkg_config_checked_PNG:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_CFITSIO_cfitsio
pkgcfg_lib_CFITSIO_cfitsio-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_gsl
pkgcfg_lib_GSL_gsl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_gslcblas
pkgcfg_lib_GSL_gslcblas-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_m
pkgcfg_lib_GSL_m-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=C:/msys64/mingw64/lib

