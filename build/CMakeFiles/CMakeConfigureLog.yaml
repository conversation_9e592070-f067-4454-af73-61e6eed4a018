
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -Aa 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -D__CLASSIC_C__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      gcc.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      gcc.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -Aa 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -D__CLASSIC_C__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      gcc.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      gcc.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1251 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
      gcc.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1251 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
      gcc.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-uxf631"
      binary: "E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-uxf631"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-uxf631'
        
        Run Build Command(s): C:/cmake/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_0434b/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_0434b.dir\\build.make CMakeFiles/cmTC_0434b.dir/build
        mingw32-make[1]: Entering directory 'E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-uxf631'
        Building C object CMakeFiles/cmTC_0434b.dir/CMakeCCompilerABI.c.obj
        C:\\msys64\\mingw64\\bin\\gcc.exe    -o CMakeFiles\\cmTC_0434b.dir\\CMakeCCompilerABI.c.obj -c C:\\cmake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c
        mingw32-make[1]: *** [CMakeFiles\\cmTC_0434b.dir\\build.make:80: CMakeFiles/cmTC_0434b.dir/CMakeCCompilerABI.c.obj] Error 1
        mingw32-make[1]: Leaving directory 'E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-uxf631'
        mingw32-make: *** [Makefile:133: cmTC_0434b/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working C compiler: C:/msys64/mingw64/bin/gcc.exe"
    directories:
      source: "E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-y5hhi5"
      binary: "E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-y5hhi5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: ""
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-y5hhi5'
        
        Run Build Command(s): C:/cmake/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_f7fe1/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_f7fe1.dir\\build.make CMakeFiles/cmTC_f7fe1.dir/build
        mingw32-make[1]: Entering directory 'E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-y5hhi5'
        Building C object CMakeFiles/cmTC_f7fe1.dir/testCCompiler.c.obj
        C:\\msys64\\mingw64\\bin\\gcc.exe    -o CMakeFiles\\cmTC_f7fe1.dir\\testCCompiler.c.obj -c E:\\github\\astrometry.net_win\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y5hhi5\\testCCompiler.c
        mingw32-make[1]: *** [CMakeFiles\\cmTC_f7fe1.dir\\build.make:80: CMakeFiles/cmTC_f7fe1.dir/testCCompiler.c.obj] Error 1
        mingw32-make[1]: Leaving directory 'E:/github/astrometry.net_win/build/CMakeFiles/CMakeScratch/TryCompile-y5hhi5'
        mingw32-make: *** [Makefile:133: cmTC_f7fe1/fast] Error 2
        
      exitCode: 2
...
