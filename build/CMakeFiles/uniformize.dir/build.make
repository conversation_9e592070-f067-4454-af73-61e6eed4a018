# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/uniformize.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/uniformize.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/uniformize.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/uniformize.dir/flags.make

CMakeFiles/uniformize.dir/codegen:
.PHONY : CMakeFiles/uniformize.dir/codegen

CMakeFiles/uniformize.dir/uniformize.c.obj: CMakeFiles/uniformize.dir/flags.make
CMakeFiles/uniformize.dir/uniformize.c.obj: E:/github/astrometry.net_win/win/uniformize.c
CMakeFiles/uniformize.dir/uniformize.c.obj: CMakeFiles/uniformize.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/uniformize.dir/uniformize.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/uniformize.dir/uniformize.c.obj -MF CMakeFiles/uniformize.dir/uniformize.c.obj.d -o CMakeFiles/uniformize.dir/uniformize.c.obj -c /E/github/astrometry.net_win/win/uniformize.c

CMakeFiles/uniformize.dir/uniformize.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/uniformize.dir/uniformize.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/uniformize.c > CMakeFiles/uniformize.dir/uniformize.c.i

CMakeFiles/uniformize.dir/uniformize.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/uniformize.dir/uniformize.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/uniformize.c -o CMakeFiles/uniformize.dir/uniformize.c.s

# Object files for target uniformize
uniformize_OBJECTS = \
"CMakeFiles/uniformize.dir/uniformize.c.obj"

# External object files for target uniformize
uniformize_EXTERNAL_OBJECTS =

bin/uniformize.exe: CMakeFiles/uniformize.dir/uniformize.c.obj
bin/uniformize.exe: CMakeFiles/uniformize.dir/build.make
bin/uniformize.exe: lib/libanutils.a
bin/uniformize.exe: lib/libanbase.a
bin/uniformize.exe: lib/libqfits.a
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/uniformize.exe"
	/C/msys64/mingw64/bin/cmake.exe -E rm -f CMakeFiles/uniformize.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/uniformize.dir/objects.a $(uniformize_OBJECTS) $(uniformize_EXTERNAL_OBJECTS)
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/uniformize.dir/objects.a -Wl,--no-whole-archive -o bin/uniformize.exe -Wl,--out-implib,lib/libuniformize.dll.a -Wl,--major-image-version,0,--minor-image-version,0  lib/libanutils.a lib/libanbase.a -lws2_32 -lregex -lm lib/libqfits.a -lgsl -lgslcblas -lm -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

# Rule to build all files generated by this target.
CMakeFiles/uniformize.dir/build: bin/uniformize.exe
.PHONY : CMakeFiles/uniformize.dir/build

CMakeFiles/uniformize.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/uniformize.dir/cmake_clean.cmake
.PHONY : CMakeFiles/uniformize.dir/clean

CMakeFiles/uniformize.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/uniformize.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/uniformize.dir/depend

