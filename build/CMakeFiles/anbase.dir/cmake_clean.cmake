file(REMOVE_RECURSE
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj.d"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj"
  "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj.d"
  "lib/libanbase.a"
  "lib/libanbase.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/anbase.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
