
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/util/an-endian.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj.d"
  "E:/github/astrometry.net_win/util/bl-sort.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj.d"
  "E:/github/astrometry.net_win/util/bl.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj.d"
  "E:/github/astrometry.net_win/util/errors.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj.d"
  "E:/github/astrometry.net_win/util/fitsbin.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj.d"
  "E:/github/astrometry.net_win/util/fitsfile.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj.d"
  "E:/github/astrometry.net_win/util/fitsioutils.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj.d"
  "E:/github/astrometry.net_win/util/ioutils.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj.d"
  "E:/github/astrometry.net_win/util/log.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj.d"
  "E:/github/astrometry.net_win/util/mathutil.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj.d"
  "E:/github/astrometry.net_win/util/tic.c" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj" "gcc" "CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
