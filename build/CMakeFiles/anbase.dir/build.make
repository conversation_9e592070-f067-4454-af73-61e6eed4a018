# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/anbase.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/anbase.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/anbase.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/anbase.dir/flags.make

CMakeFiles/anbase.dir/codegen:
.PHONY : CMakeFiles/anbase.dir/codegen

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj: E:/github/astrometry.net_win/util/ioutils.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj -c /E/github/astrometry.net_win/util/ioutils.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/ioutils.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/ioutils.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj: E:/github/astrometry.net_win/util/mathutil.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj -c /E/github/astrometry.net_win/util/mathutil.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/mathutil.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/mathutil.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj: E:/github/astrometry.net_win/util/fitsioutils.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj -c /E/github/astrometry.net_win/util/fitsioutils.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/fitsioutils.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/fitsioutils.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj: E:/github/astrometry.net_win/util/fitsbin.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj -c /E/github/astrometry.net_win/util/fitsbin.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/fitsbin.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/fitsbin.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj: E:/github/astrometry.net_win/util/an-endian.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj -c /E/github/astrometry.net_win/util/an-endian.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/an-endian.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/an-endian.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj: E:/github/astrometry.net_win/util/fitsfile.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj -c /E/github/astrometry.net_win/util/fitsfile.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/fitsfile.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/fitsfile.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj: E:/github/astrometry.net_win/util/log.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj -c /E/github/astrometry.net_win/util/log.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/log.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/log.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj: E:/github/astrometry.net_win/util/errors.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj -c /E/github/astrometry.net_win/util/errors.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/errors.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/errors.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj: E:/github/astrometry.net_win/util/tic.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj -c /E/github/astrometry.net_win/util/tic.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/tic.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/tic.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj: E:/github/astrometry.net_win/util/bl.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj -c /E/github/astrometry.net_win/util/bl.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/bl.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/bl.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.s

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj: CMakeFiles/anbase.dir/flags.make
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj: E:/github/astrometry.net_win/util/bl-sort.c
CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj: CMakeFiles/anbase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj -MF CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj.d -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj -c /E/github/astrometry.net_win/util/bl-sort.c

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/bl-sort.c > CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.i

CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/bl-sort.c -o CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.s

# Object files for target anbase
anbase_OBJECTS = \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj" \
"CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj"

# External object files for target anbase
anbase_EXTERNAL_OBJECTS =

lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/ioutils.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/mathutil.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsioutils.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsbin.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/an-endian.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/fitsfile.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/log.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/errors.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/tic.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/E_/github/astrometry.net_win/util/bl-sort.c.obj
lib/libanbase.a: CMakeFiles/anbase.dir/build.make
lib/libanbase.a: CMakeFiles/anbase.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking C static library lib/libanbase.a"
	$(CMAKE_COMMAND) -P CMakeFiles/anbase.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/anbase.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/anbase.dir/build: lib/libanbase.a
.PHONY : CMakeFiles/anbase.dir/build

CMakeFiles/anbase.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/anbase.dir/cmake_clean.cmake
.PHONY : CMakeFiles/anbase.dir/clean

CMakeFiles/anbase.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/anbase.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/anbase.dir/depend

