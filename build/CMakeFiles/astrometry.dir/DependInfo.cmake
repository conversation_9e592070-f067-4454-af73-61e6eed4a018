
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/solver/augment-xylist.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj.d"
  "E:/github/astrometry.net_win/solver/build-index.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj.d"
  "E:/github/astrometry.net_win/solver/codefile.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj.d"
  "E:/github/astrometry.net_win/solver/codetree.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj.d"
  "E:/github/astrometry.net_win/solver/cut-table.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj.d"
  "E:/github/astrometry.net_win/solver/engine.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj.d"
  "E:/github/astrometry.net_win/solver/fits-guess-scale.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj.d"
  "E:/github/astrometry.net_win/solver/hpquads.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj.d"
  "E:/github/astrometry.net_win/solver/image2xy-files.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj.d"
  "E:/github/astrometry.net_win/solver/merge-index.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj.d"
  "E:/github/astrometry.net_win/solver/new-wcs.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj.d"
  "E:/github/astrometry.net_win/solver/onefield.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj.d"
  "E:/github/astrometry.net_win/solver/quad-builder.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj.d"
  "E:/github/astrometry.net_win/solver/quad-utils.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj.d"
  "E:/github/astrometry.net_win/solver/resort-xylist.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj.d"
  "E:/github/astrometry.net_win/solver/solvedfile.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj.d"
  "E:/github/astrometry.net_win/solver/solver.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj.d"
  "E:/github/astrometry.net_win/solver/solverutils.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj.d"
  "E:/github/astrometry.net_win/solver/startree.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj.d"
  "E:/github/astrometry.net_win/solver/tweak.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj.d"
  "E:/github/astrometry.net_win/solver/tweak2.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj.d"
  "E:/github/astrometry.net_win/solver/uniformize-catalog.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj.d"
  "E:/github/astrometry.net_win/solver/unpermute-quads.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj.d"
  "E:/github/astrometry.net_win/solver/unpermute-stars.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj.d"
  "E:/github/astrometry.net_win/solver/verify.c" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj" "gcc" "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
