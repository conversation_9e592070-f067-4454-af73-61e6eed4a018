file(REMOVE_RECURSE
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj.d"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj"
  "CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj.d"
  "lib/libastrometry.a"
  "lib/libastrometry.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/astrometry.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
