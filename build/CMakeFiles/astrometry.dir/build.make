# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/astrometry.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/astrometry.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/astrometry.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/astrometry.dir/flags.make

CMakeFiles/astrometry.dir/codegen:
.PHONY : CMakeFiles/astrometry.dir/codegen

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj: E:/github/astrometry.net_win/solver/engine.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj -c /E/github/astrometry.net_win/solver/engine.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/engine.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/engine.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj: E:/github/astrometry.net_win/solver/solverutils.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj -c /E/github/astrometry.net_win/solver/solverutils.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/solverutils.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/solverutils.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj: E:/github/astrometry.net_win/solver/onefield.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj -c /E/github/astrometry.net_win/solver/onefield.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/onefield.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/onefield.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj: E:/github/astrometry.net_win/solver/solver.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj -c /E/github/astrometry.net_win/solver/solver.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/solver.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/solver.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj: E:/github/astrometry.net_win/solver/quad-utils.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj -c /E/github/astrometry.net_win/solver/quad-utils.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/quad-utils.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/quad-utils.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj: E:/github/astrometry.net_win/solver/solvedfile.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj -c /E/github/astrometry.net_win/solver/solvedfile.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/solvedfile.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/solvedfile.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj: E:/github/astrometry.net_win/solver/tweak2.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj -c /E/github/astrometry.net_win/solver/tweak2.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/tweak2.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/tweak2.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj: E:/github/astrometry.net_win/solver/verify.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj -c /E/github/astrometry.net_win/solver/verify.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/verify.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/verify.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj: E:/github/astrometry.net_win/solver/tweak.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj -c /E/github/astrometry.net_win/solver/tweak.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/tweak.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/tweak.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj: E:/github/astrometry.net_win/solver/new-wcs.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj -c /E/github/astrometry.net_win/solver/new-wcs.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/new-wcs.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/new-wcs.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj: E:/github/astrometry.net_win/solver/fits-guess-scale.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj -c /E/github/astrometry.net_win/solver/fits-guess-scale.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/fits-guess-scale.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/fits-guess-scale.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj: E:/github/astrometry.net_win/solver/cut-table.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj -c /E/github/astrometry.net_win/solver/cut-table.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/cut-table.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/cut-table.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj: E:/github/astrometry.net_win/solver/resort-xylist.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj -c /E/github/astrometry.net_win/solver/resort-xylist.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/resort-xylist.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/resort-xylist.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj: E:/github/astrometry.net_win/solver/build-index.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj -c /E/github/astrometry.net_win/solver/build-index.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/build-index.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/build-index.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj: E:/github/astrometry.net_win/solver/uniformize-catalog.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj -c /E/github/astrometry.net_win/solver/uniformize-catalog.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/uniformize-catalog.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/uniformize-catalog.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj: E:/github/astrometry.net_win/solver/startree.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj -c /E/github/astrometry.net_win/solver/startree.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/startree.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/startree.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj: E:/github/astrometry.net_win/solver/hpquads.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj -c /E/github/astrometry.net_win/solver/hpquads.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/hpquads.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/hpquads.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj: E:/github/astrometry.net_win/solver/quad-builder.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj -c /E/github/astrometry.net_win/solver/quad-builder.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/quad-builder.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/quad-builder.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj: E:/github/astrometry.net_win/solver/codefile.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj -c /E/github/astrometry.net_win/solver/codefile.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/codefile.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/codefile.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj: E:/github/astrometry.net_win/solver/codetree.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj -c /E/github/astrometry.net_win/solver/codetree.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/codetree.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/codetree.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj: E:/github/astrometry.net_win/solver/unpermute-stars.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj -c /E/github/astrometry.net_win/solver/unpermute-stars.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/unpermute-stars.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/unpermute-stars.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj: E:/github/astrometry.net_win/solver/unpermute-quads.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj -c /E/github/astrometry.net_win/solver/unpermute-quads.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/unpermute-quads.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/unpermute-quads.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj: E:/github/astrometry.net_win/solver/merge-index.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj -c /E/github/astrometry.net_win/solver/merge-index.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/merge-index.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/merge-index.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj: E:/github/astrometry.net_win/solver/augment-xylist.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj -c /E/github/astrometry.net_win/solver/augment-xylist.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/augment-xylist.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/augment-xylist.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.s

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj: CMakeFiles/astrometry.dir/flags.make
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj: E:/github/astrometry.net_win/solver/image2xy-files.c
CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj: CMakeFiles/astrometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj -MF CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj.d -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj -c /E/github/astrometry.net_win/solver/image2xy-files.c

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/image2xy-files.c > CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.i

CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/image2xy-files.c -o CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.s

# Object files for target astrometry
astrometry_OBJECTS = \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj" \
"CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj"

# External object files for target astrometry
astrometry_EXTERNAL_OBJECTS =

lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/engine.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solverutils.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/onefield.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solver.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-utils.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/solvedfile.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak2.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/verify.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/tweak.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/new-wcs.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/fits-guess-scale.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/cut-table.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/resort-xylist.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/build-index.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/uniformize-catalog.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/startree.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/hpquads.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/quad-builder.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codefile.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/codetree.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-stars.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/unpermute-quads.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/merge-index.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/augment-xylist.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/E_/github/astrometry.net_win/solver/image2xy-files.c.obj
lib/libastrometry.a: CMakeFiles/astrometry.dir/build.make
lib/libastrometry.a: CMakeFiles/astrometry.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Linking C static library lib/libastrometry.a"
	$(CMAKE_COMMAND) -P CMakeFiles/astrometry.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/astrometry.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/astrometry.dir/build: lib/libastrometry.a
.PHONY : CMakeFiles/astrometry.dir/build

CMakeFiles/astrometry.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/astrometry.dir/cmake_clean.cmake
.PHONY : CMakeFiles/astrometry.dir/clean

CMakeFiles/astrometry.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/astrometry.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/astrometry.dir/depend

