# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/solve-field.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/solve-field.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/solve-field.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/solve-field.dir/flags.make

CMakeFiles/solve-field.dir/codegen:
.PHONY : CMakeFiles/solve-field.dir/codegen

CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj: CMakeFiles/solve-field.dir/flags.make
CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj: E:/github/astrometry.net_win/solver/solve-field.c
CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj: CMakeFiles/solve-field.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj -MF CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj.d -o CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj -c /E/github/astrometry.net_win/solver/solve-field.c

CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/solve-field.c > CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.i

CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/solve-field.c -o CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.s

# Object files for target solve-field
solve__field_OBJECTS = \
"CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj"

# External object files for target solve-field
solve__field_EXTERNAL_OBJECTS =

bin/solve-field.exe: CMakeFiles/solve-field.dir/E_/github/astrometry.net_win/solver/solve-field.c.obj
bin/solve-field.exe: CMakeFiles/solve-field.dir/build.make
bin/solve-field.exe: lib/libastrometry.a
bin/solve-field.exe: lib/libcatalogs.a
bin/solve-field.exe: lib/libanutils.a
bin/solve-field.exe: lib/libanfiles.a
bin/solve-field.exe: lib/libanbase.a
bin/solve-field.exe: lib/libqfits.a
bin/solve-field.exe: lib/liblibkd.a
bin/solve-field.exe: C:/msys64/mingw64/lib/libregex.dll.a
bin/solve-field.exe: lib/libanutils.a
bin/solve-field.exe: lib/libanbase.a
bin/solve-field.exe: lib/libqfits.a
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/solve-field.exe"
	/C/msys64/mingw64/bin/cmake.exe -E rm -f CMakeFiles/solve-field.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/solve-field.dir/objects.a $(solve__field_OBJECTS) $(solve__field_EXTERNAL_OBJECTS)
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/solve-field.dir/objects.a -Wl,--no-whole-archive -o bin/solve-field.exe -Wl,--out-implib,lib/libsolve-field.dll.a -Wl,--major-image-version,0,--minor-image-version,0  lib/libastrometry.a lib/libcatalogs.a lib/libanutils.a lib/libanfiles.a lib/libanbase.a lib/libqfits.a lib/liblibkd.a -lcfitsio -lm -lgsl -lgslcblas -lm -lws2_32 /C/msys64/mingw64/lib/libregex.dll.a lib/libanutils.a lib/libanbase.a lib/libqfits.a -lgsl -lgslcblas -lm -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

# Rule to build all files generated by this target.
CMakeFiles/solve-field.dir/build: bin/solve-field.exe
.PHONY : CMakeFiles/solve-field.dir/build

CMakeFiles/solve-field.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/solve-field.dir/cmake_clean.cmake
.PHONY : CMakeFiles/solve-field.dir/clean

CMakeFiles/solve-field.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/solve-field.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/solve-field.dir/depend

