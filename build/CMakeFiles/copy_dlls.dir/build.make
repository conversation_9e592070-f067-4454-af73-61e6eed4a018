# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Utility rule file for copy_dlls.

# Include any custom commands dependencies for this target.
include CMakeFiles/copy_dlls.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/copy_dlls.dir/progress.make

CMakeFiles/copy_dlls:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Copying DLL dependencies, scripts and NetPBM tools"
	/C/CMake/bin/cmake.exe -E echo "Copying required DLLs..."
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libgcc_s_seh-1.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libwinpthread-1.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libcfitsio-10.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libgsl-28.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libsystre-0.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libcurl-4.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libintl-8.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libtre-5.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libbrotlidec.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libbrotlicommon.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libiconv-2.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libidn2-0.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libssl-3-x64.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libcrypto-3-x64.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libnghttp2-14.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libpsl-5.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libunistring-5.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libzstd.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libssh2-1.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libnghttp3-9.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libngtcp2-16.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libngtcp2_crypto_ossl-0.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different C:/msys64/mingw64/bin/libstdc++-6.dll E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -E copy_if_different E:/github/astrometry.net_win/win/image2pnm.bat E:/github/astrometry.net_win/build/bin
	/C/CMake/bin/cmake.exe -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:/github/astrometry.net_win/build/bin -P E:/github/astrometry.net_win/win/copy_netpbm_tools.cmake

CMakeFiles/copy_dlls.dir/codegen:
.PHONY : CMakeFiles/copy_dlls.dir/codegen

copy_dlls: CMakeFiles/copy_dlls
copy_dlls: CMakeFiles/copy_dlls.dir/build.make
.PHONY : copy_dlls

# Rule to build all files generated by this target.
CMakeFiles/copy_dlls.dir/build: copy_dlls
.PHONY : CMakeFiles/copy_dlls.dir/build

CMakeFiles/copy_dlls.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/copy_dlls.dir/cmake_clean.cmake
.PHONY : CMakeFiles/copy_dlls.dir/clean

CMakeFiles/copy_dlls.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/copy_dlls.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/copy_dlls.dir/depend

