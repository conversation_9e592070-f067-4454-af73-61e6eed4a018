CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj: \
 E:/github/astrometry.net_win/util/fit-wcs.c \
 C:/msys64/mingw64/include/math.h C:/msys64/mingw64/include/crtdefs.h \
 C:/msys64/mingw64/include/corecrt.h C:/msys64/mingw64/include/_mingw.h \
 C:/msys64/mingw64/include/_mingw_mac.h \
 C:/msys64/mingw64/include/_mingw_secapi.h \
 C:/msys64/mingw64/include/vadefs.h \
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 C:/msys64/mingw64/include/assert.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_complex_long_double.h \
 C:/msys64/mingw64/include/stdlib.h \
 C:/msys64/mingw64/include/corecrt_wstdlib.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
 C:/msys64/mingw64/include/limits.h \
 C:/msys64/mingw64/include/sec_api/stdlib_s.h \
 C:/msys64/mingw64/include/malloc.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
 C:/msys64/mingw64/include/errno.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_types.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_errno.h \
 C:/msys64/mingw64/include/stdio.h \
 C:/msys64/mingw64/include/corecrt_stdio_config.h \
 C:/msys64/mingw64/include/_mingw_off_t.h \
 C:/msys64/mingw64/include/swprintf.inl \
 C:/msys64/mingw64/include/sec_api/stdio_s.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_complex.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_check_range.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_complex_long_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_long_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_inline.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_long_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_complex.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_complex_long_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_complex_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_complex_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_complex_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_complex_float.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_complex_float.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_float.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_float.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_complex_float.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_long_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_double.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_float.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_ulong.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_ulong.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_ulong.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_long.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_long.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_long.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_uint.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_uint.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_uint.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_int.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_int.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_int.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_ushort.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_ushort.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_ushort.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_short.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_short.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_short.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_uchar.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_uchar.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_uchar.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_matrix_char.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector_char.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_block_char.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_linalg.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_mode.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_permutation.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_vector.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_blas.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_blas_types.h \
 E:/github/astrometry.net_win/gsl-an/gsl/gsl_cblas.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
 C:/msys64/mingw64/include/stddef.h \
 E:/github/astrometry.net_win/include/astrometry/os-features.h \
 E:/github/astrometry.net_win/include/astrometry/os-features-config.h \
 C:/msys64/mingw64/include/sys/param.h \
 C:/msys64/mingw64/include/sys/types.h \
 E:/github/astrometry.net_win/include/astrometry/fit-wcs.h \
 E:/github/astrometry.net_win/include/astrometry/sip.h \
 E:/github/astrometry.net_win/include/astrometry/an-bool.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
 C:/msys64/mingw64/include/stdint.h \
 E:/github/astrometry.net_win/include/astrometry/keywords.h \
 E:/github/astrometry.net_win/include/astrometry/starkd.h \
 E:/github/astrometry.net_win/include/astrometry/kdtree.h \
 E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
 E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
 E:/github/astrometry.net_win/include/astrometry/anqfits.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
 C:/msys64/mingw64/include/string.h \
 C:/msys64/mingw64/include/sec_api/string_s.h \
 C:/msys64/mingw64/include/unistd.h C:/msys64/mingw64/include/io.h \
 C:/msys64/mingw64/include/process.h \
 C:/msys64/mingw64/include/corecrt_startup.h \
 C:/msys64/mingw64/include/getopt.h \
 C:/msys64/mingw64/include/pthread_unistd.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
 E:/github/astrometry.net_win/include/astrometry/bl.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
 C:/msys64/mingw64/include/stdarg.h \
 C:/msys64/mingw64/include/_mingw_stdarg.h \
 E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
 E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
 E:/github/astrometry.net_win/include/astrometry/fitstable.h \
 E:/github/astrometry.net_win/include/astrometry/ioutils.h \
 C:/msys64/mingw64/include/time.h C:/msys64/mingw64/include/sys/timeb.h \
 C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
 C:/msys64/mingw64/include/_timeval.h \
 C:/msys64/mingw64/include/pthread_time.h \
 C:/msys64/mingw64/include/pthread_compat.h \
 E:/github/astrometry.net_win/include/astrometry/starutil.h \
 E:/github/astrometry.net_win/include/astrometry/mathutil.h \
 E:/github/astrometry.net_win/include/astrometry/bl.h \
 E:/github/astrometry.net_win/include/astrometry/sip.h \
 E:/github/astrometry.net_win/include/astrometry/sip_qfits.h \
 E:/github/astrometry.net_win/include/astrometry/log.h \
 E:/github/astrometry.net_win/include/astrometry/errors.h \
 C:/msys64/mingw64/include/regex.h C:/msys64/mingw64/include/tre/tre.h \
 C:/msys64/mingw64/include/tre/tre-config.h \
 C:/msys64/mingw64/include/wchar.h \
 C:/msys64/mingw64/include/corecrt_wctype.h \
 C:/msys64/mingw64/include/_mingw_stat64.h \
 C:/msys64/mingw64/include/sec_api/wchar_s.h \
 E:/github/astrometry.net_win/include/astrometry/gslutils.h \
 E:/github/astrometry.net_win/include/astrometry/sip-utils.h
