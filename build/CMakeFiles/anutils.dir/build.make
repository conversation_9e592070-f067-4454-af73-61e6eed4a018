# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/anutils.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/anutils.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/anutils.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/anutils.dir/flags.make

CMakeFiles/anutils.dir/codegen:
.PHONY : CMakeFiles/anutils.dir/codegen

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj: E:/github/astrometry.net_win/util/starutil.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj -c /E/github/astrometry.net_win/util/starutil.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/starutil.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/starutil.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj: E:/github/astrometry.net_win/util/sip.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj -c /E/github/astrometry.net_win/util/sip.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/sip.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/sip.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj: E:/github/astrometry.net_win/util/sip_qfits.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj -c /E/github/astrometry.net_win/util/sip_qfits.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/sip_qfits.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/sip_qfits.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj: E:/github/astrometry.net_win/util/sip-utils.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj -c /E/github/astrometry.net_win/util/sip-utils.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/sip-utils.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/sip-utils.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj: E:/github/astrometry.net_win/util/fit-wcs.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj -c /E/github/astrometry.net_win/util/fit-wcs.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/fit-wcs.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/fit-wcs.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj: E:/github/astrometry.net_win/util/gslutils.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj -c /E/github/astrometry.net_win/util/gslutils.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/gslutils.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/gslutils.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj: E:/github/astrometry.net_win/util/xylist.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj -c /E/github/astrometry.net_win/util/xylist.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/xylist.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/xylist.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj: E:/github/astrometry.net_win/util/datalog.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj -c /E/github/astrometry.net_win/util/datalog.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/datalog.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/datalog.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj: E:/github/astrometry.net_win/util/fileutils.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj -c /E/github/astrometry.net_win/util/fileutils.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/fileutils.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/fileutils.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj: E:/github/astrometry.net_win/util/permutedsort.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj -c /E/github/astrometry.net_win/util/permutedsort.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/permutedsort.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/permutedsort.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj: E:/github/astrometry.net_win/util/resample.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj -c /E/github/astrometry.net_win/util/resample.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/resample.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/resample.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj: E:/github/astrometry.net_win/util/ctmf.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj -c /E/github/astrometry.net_win/util/ctmf.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/ctmf.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/ctmf.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj: E:/github/astrometry.net_win/util/dselip.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj -c /E/github/astrometry.net_win/util/dselip.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dselip.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dselip.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj: E:/github/astrometry.net_win/util/dsmooth.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj -c /E/github/astrometry.net_win/util/dsmooth.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dsmooth.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dsmooth.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj: E:/github/astrometry.net_win/util/dallpeaks.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj -c /E/github/astrometry.net_win/util/dallpeaks.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dallpeaks.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dallpeaks.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj: E:/github/astrometry.net_win/util/dcen3x3.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj -c /E/github/astrometry.net_win/util/dcen3x3.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dcen3x3.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dcen3x3.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj: E:/github/astrometry.net_win/util/dfind.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj -c /E/github/astrometry.net_win/util/dfind.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dfind.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dfind.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj: E:/github/astrometry.net_win/util/dobjects.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj -c /E/github/astrometry.net_win/util/dobjects.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dobjects.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dobjects.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj: E:/github/astrometry.net_win/util/dmedsmooth.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj -c /E/github/astrometry.net_win/util/dmedsmooth.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dmedsmooth.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dmedsmooth.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj: E:/github/astrometry.net_win/util/dpeaks.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj -c /E/github/astrometry.net_win/util/dpeaks.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dpeaks.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dpeaks.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj: E:/github/astrometry.net_win/util/dsigma.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj -c /E/github/astrometry.net_win/util/dsigma.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/dsigma.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/dsigma.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj: E:/github/astrometry.net_win/util/simplexy.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj -c /E/github/astrometry.net_win/util/simplexy.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/simplexy.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/simplexy.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj: E:/github/astrometry.net_win/util/image2xy.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj -c /E/github/astrometry.net_win/util/image2xy.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/image2xy.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/image2xy.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj: E:/github/astrometry.net_win/util/healpix.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj -c /E/github/astrometry.net_win/util/healpix.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/healpix.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/healpix.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj: E:/github/astrometry.net_win/util/an-opts.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj -c /E/github/astrometry.net_win/util/an-opts.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/an-opts.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/an-opts.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj: E:/github/astrometry.net_win/util/wcs-rd2xy.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj -c /E/github/astrometry.net_win/util/wcs-rd2xy.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/wcs-rd2xy.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/wcs-rd2xy.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj: E:/github/astrometry.net_win/util/scamp.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj -c /E/github/astrometry.net_win/util/scamp.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/scamp.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/scamp.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj: E:/github/astrometry.net_win/util/tabsort.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj -c /E/github/astrometry.net_win/util/tabsort.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/tabsort.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/tabsort.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.s

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj: CMakeFiles/anutils.dir/flags.make
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj: E:/github/astrometry.net_win/util/anwcs.c
CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj: CMakeFiles/anutils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj -MF CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj.d -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj -c /E/github/astrometry.net_win/util/anwcs.c

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/anwcs.c > CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.i

CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/anwcs.c -o CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.s

# Object files for target anutils
anutils_OBJECTS = \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj" \
"CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj"

# External object files for target anutils
anutils_EXTERNAL_OBJECTS =

lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj
lib/libanutils.a: CMakeFiles/anutils.dir/build.make
lib/libanutils.a: CMakeFiles/anutils.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Linking C static library lib/libanutils.a"
	$(CMAKE_COMMAND) -P CMakeFiles/anutils.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/anutils.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/anutils.dir/build: lib/libanutils.a
.PHONY : CMakeFiles/anutils.dir/build

CMakeFiles/anutils.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/anutils.dir/cmake_clean.cmake
.PHONY : CMakeFiles/anutils.dir/clean

CMakeFiles/anutils.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/anutils.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/anutils.dir/depend

