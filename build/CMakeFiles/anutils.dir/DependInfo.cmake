
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/util/an-opts.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/an-opts.c.obj.d"
  "E:/github/astrometry.net_win/util/anwcs.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/anwcs.c.obj.d"
  "E:/github/astrometry.net_win/util/ctmf.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/ctmf.c.obj.d"
  "E:/github/astrometry.net_win/util/dallpeaks.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dallpeaks.c.obj.d"
  "E:/github/astrometry.net_win/util/datalog.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/datalog.c.obj.d"
  "E:/github/astrometry.net_win/util/dcen3x3.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dcen3x3.c.obj.d"
  "E:/github/astrometry.net_win/util/dfind.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dfind.c.obj.d"
  "E:/github/astrometry.net_win/util/dmedsmooth.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dmedsmooth.c.obj.d"
  "E:/github/astrometry.net_win/util/dobjects.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dobjects.c.obj.d"
  "E:/github/astrometry.net_win/util/dpeaks.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dpeaks.c.obj.d"
  "E:/github/astrometry.net_win/util/dselip.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dselip.c.obj.d"
  "E:/github/astrometry.net_win/util/dsigma.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsigma.c.obj.d"
  "E:/github/astrometry.net_win/util/dsmooth.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/dsmooth.c.obj.d"
  "E:/github/astrometry.net_win/util/fileutils.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fileutils.c.obj.d"
  "E:/github/astrometry.net_win/util/fit-wcs.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/fit-wcs.c.obj.d"
  "E:/github/astrometry.net_win/util/gslutils.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/gslutils.c.obj.d"
  "E:/github/astrometry.net_win/util/healpix.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/healpix.c.obj.d"
  "E:/github/astrometry.net_win/util/image2xy.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/image2xy.c.obj.d"
  "E:/github/astrometry.net_win/util/permutedsort.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/permutedsort.c.obj.d"
  "E:/github/astrometry.net_win/util/resample.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/resample.c.obj.d"
  "E:/github/astrometry.net_win/util/scamp.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/scamp.c.obj.d"
  "E:/github/astrometry.net_win/util/simplexy.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/simplexy.c.obj.d"
  "E:/github/astrometry.net_win/util/sip-utils.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip-utils.c.obj.d"
  "E:/github/astrometry.net_win/util/sip.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip.c.obj.d"
  "E:/github/astrometry.net_win/util/sip_qfits.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/sip_qfits.c.obj.d"
  "E:/github/astrometry.net_win/util/starutil.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/starutil.c.obj.d"
  "E:/github/astrometry.net_win/util/tabsort.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/tabsort.c.obj.d"
  "E:/github/astrometry.net_win/util/wcs-rd2xy.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/wcs-rd2xy.c.obj.d"
  "E:/github/astrometry.net_win/util/xylist.c" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj" "gcc" "CMakeFiles/anutils.dir/E_/github/astrometry.net_win/util/xylist.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
