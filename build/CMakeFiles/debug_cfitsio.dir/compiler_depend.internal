# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

CMakeFiles/debug_cfitsio.dir/debug_cfitsio.c.obj
 E:/github/astrometry.net_win/win/debug_cfitsio.c
 C:/msys64/mingw64/include/_mingw.h
 C:/msys64/mingw64/include/_mingw_mac.h
 C:/msys64/mingw64/include/_mingw_off_t.h
 C:/msys64/mingw64/include/_mingw_secapi.h
 C:/msys64/mingw64/include/corecrt.h
 C:/msys64/mingw64/include/corecrt_stdio_config.h
 C:/msys64/mingw64/include/corecrt_wstdlib.h
 C:/msys64/mingw64/include/crtdefs.h
 C:/msys64/mingw64/include/errno.h
 C:/msys64/mingw64/include/fitsio.h
 C:/msys64/mingw64/include/limits.h
 C:/msys64/mingw64/include/longnam.h
 C:/msys64/mingw64/include/malloc.h
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h
 C:/msys64/mingw64/include/sec_api/stdio_s.h
 C:/msys64/mingw64/include/sec_api/stdlib_s.h
 C:/msys64/mingw64/include/sec_api/string_s.h
 C:/msys64/mingw64/include/stdio.h
 C:/msys64/mingw64/include/stdlib.h
 C:/msys64/mingw64/include/string.h
 C:/msys64/mingw64/include/swprintf.inl
 C:/msys64/mingw64/include/vadefs.h
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h

