file(REMOVE_RECURSE
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj.d"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj"
  "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj.d"
  "lib/libanfiles.a"
  "lib/libanfiles.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/anfiles.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
