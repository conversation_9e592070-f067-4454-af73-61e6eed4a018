# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj: E:/github/astrometry.net_win/util/codekd.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/codekd.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj: E:/github/astrometry.net_win/util/fitstable.c \
  C:/msys64/mingw64/include/_bsd_types.h \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_mingw_unicode.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/apiset.h \
  C:/msys64/mingw64/include/apisetcconv.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/basetsd.h \
  C:/msys64/mingw64/include/bcrypt.h \
  C:/msys64/mingw64/include/bemapiset.h \
  C:/msys64/mingw64/include/cderr.h \
  C:/msys64/mingw64/include/cguid.h \
  C:/msys64/mingw64/include/combaseapi.h \
  C:/msys64/mingw64/include/commdlg.h \
  C:/msys64/mingw64/include/concurrencysal.h \
  C:/msys64/mingw64/include/consoleapi.h \
  C:/msys64/mingw64/include/consoleapi2.h \
  C:/msys64/mingw64/include/consoleapi3.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/ctype.h \
  C:/msys64/mingw64/include/datetimeapi.h \
  C:/msys64/mingw64/include/dde.h \
  C:/msys64/mingw64/include/ddeml.h \
  C:/msys64/mingw64/include/debugapi.h \
  C:/msys64/mingw64/include/dlgs.h \
  C:/msys64/mingw64/include/dpapi.h \
  C:/msys64/mingw64/include/driverspecs.h \
  C:/msys64/mingw64/include/errhandlingapi.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/excpt.h \
  C:/msys64/mingw64/include/fibersapi.h \
  C:/msys64/mingw64/include/fileapi.h \
  C:/msys64/mingw64/include/fltwinerror.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/guiddef.h \
  C:/msys64/mingw64/include/handleapi.h \
  C:/msys64/mingw64/include/heapapi.h \
  C:/msys64/mingw64/include/imm.h \
  C:/msys64/mingw64/include/inaddr.h \
  C:/msys64/mingw64/include/interlockedapi.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/ioapiset.h \
  C:/msys64/mingw64/include/jobapi.h \
  C:/msys64/mingw64/include/joystickapi.h \
  C:/msys64/mingw64/include/ktmtypes.h \
  C:/msys64/mingw64/include/libloaderapi.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/lzexpand.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/mciapi.h \
  C:/msys64/mingw64/include/mcx.h \
  C:/msys64/mingw64/include/memoryapi.h \
  C:/msys64/mingw64/include/minwinbase.h \
  C:/msys64/mingw64/include/minwindef.h \
  C:/msys64/mingw64/include/mmeapi.h \
  C:/msys64/mingw64/include/mmiscapi.h \
  C:/msys64/mingw64/include/mmiscapi2.h \
  C:/msys64/mingw64/include/mmsyscom.h \
  C:/msys64/mingw64/include/mmsystem.h \
  C:/msys64/mingw64/include/msxml.h \
  C:/msys64/mingw64/include/namedpipeapi.h \
  C:/msys64/mingw64/include/namespaceapi.h \
  C:/msys64/mingw64/include/nb30.h \
  C:/msys64/mingw64/include/ncrypt.h \
  C:/msys64/mingw64/include/oaidl.h \
  C:/msys64/mingw64/include/objbase.h \
  C:/msys64/mingw64/include/objidl.h \
  C:/msys64/mingw64/include/objidlbase.h \
  C:/msys64/mingw64/include/ole2.h \
  C:/msys64/mingw64/include/oleauto.h \
  C:/msys64/mingw64/include/oleidl.h \
  C:/msys64/mingw64/include/playsoundapi.h \
  C:/msys64/mingw64/include/poppack.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/processenv.h \
  C:/msys64/mingw64/include/processthreadsapi.h \
  C:/msys64/mingw64/include/processtopologyapi.h \
  C:/msys64/mingw64/include/profileapi.h \
  C:/msys64/mingw64/include/propidl.h \
  C:/msys64/mingw64/include/prsht.h \
  C:/msys64/mingw64/include/psdk_inc/_fd_types.h \
  C:/msys64/mingw64/include/psdk_inc/_ip_types.h \
  C:/msys64/mingw64/include/psdk_inc/_socket_types.h \
  C:/msys64/mingw64/include/psdk_inc/_ws1_undef.h \
  C:/msys64/mingw64/include/psdk_inc/_wsa_errnos.h \
  C:/msys64/mingw64/include/psdk_inc/_wsadata.h \
  C:/msys64/mingw64/include/psdk_inc/intrin-impl.h \
  C:/msys64/mingw64/include/pshpack1.h \
  C:/msys64/mingw64/include/pshpack2.h \
  C:/msys64/mingw64/include/pshpack4.h \
  C:/msys64/mingw64/include/pshpack8.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/qos.h \
  C:/msys64/mingw64/include/realtimeapiset.h \
  C:/msys64/mingw64/include/reason.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/rpc.h \
  C:/msys64/mingw64/include/rpcasync.h \
  C:/msys64/mingw64/include/rpcdce.h \
  C:/msys64/mingw64/include/rpcdcep.h \
  C:/msys64/mingw64/include/rpcndr.h \
  C:/msys64/mingw64/include/rpcnsi.h \
  C:/msys64/mingw64/include/rpcnsip.h \
  C:/msys64/mingw64/include/rpcnterr.h \
  C:/msys64/mingw64/include/rpcsal.h \
  C:/msys64/mingw64/include/sal.h \
  C:/msys64/mingw64/include/sdkddkver.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/stralign_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/securityappcontainer.h \
  C:/msys64/mingw64/include/securitybaseapi.h \
  C:/msys64/mingw64/include/servprov.h \
  C:/msys64/mingw64/include/shellapi.h \
  C:/msys64/mingw64/include/specstrings.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/stralign.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/stringapiset.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/synchapi.h \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/sysinfoapi.h \
  C:/msys64/mingw64/include/systemtopologyapi.h \
  C:/msys64/mingw64/include/threadpoolapiset.h \
  C:/msys64/mingw64/include/threadpoollegacyapiset.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/timeapi.h \
  C:/msys64/mingw64/include/timezoneapi.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/tvout.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/unknwn.h \
  C:/msys64/mingw64/include/unknwnbase.h \
  C:/msys64/mingw64/include/urlmon.h \
  C:/msys64/mingw64/include/utilapiset.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/virtdisk.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/include/winapifamily.h \
  C:/msys64/mingw64/include/winbase.h \
  C:/msys64/mingw64/include/wincon.h \
  C:/msys64/mingw64/include/wincontypes.h \
  C:/msys64/mingw64/include/wincrypt.h \
  C:/msys64/mingw64/include/windef.h \
  C:/msys64/mingw64/include/windows.h \
  C:/msys64/mingw64/include/winefs.h \
  C:/msys64/mingw64/include/winerror.h \
  C:/msys64/mingw64/include/wingdi.h \
  C:/msys64/mingw64/include/winioctl.h \
  C:/msys64/mingw64/include/winnetwk.h \
  C:/msys64/mingw64/include/winnls.h \
  C:/msys64/mingw64/include/winnt.h \
  C:/msys64/mingw64/include/winperf.h \
  C:/msys64/mingw64/include/winreg.h \
  C:/msys64/mingw64/include/winscard.h \
  C:/msys64/mingw64/include/winsmcrd.h \
  C:/msys64/mingw64/include/winsock.h \
  C:/msys64/mingw64/include/winsock2.h \
  C:/msys64/mingw64/include/winspool.h \
  C:/msys64/mingw64/include/winsvc.h \
  C:/msys64/mingw64/include/winuser.h \
  C:/msys64/mingw64/include/winver.h \
  C:/msys64/mingw64/include/wnnc.h \
  C:/msys64/mingw64/include/wow64apiset.h \
  C:/msys64/mingw64/include/ws2def.h \
  C:/msys64/mingw64/include/wtypes.h \
  C:/msys64/mingw64/include/wtypesbase.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/adxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/ammintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxavx512intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxbf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxcomplexintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxfp16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxfp8intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxint8intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxmovrsintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtf32intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtileintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtransposeintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512bf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512convertintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512mediaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512minmaxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512satcvtintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2bf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2convertintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2copyintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2mediaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2minmaxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2satcvtintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx2intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bf16vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bitalgintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bitalgvlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bwintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512cdintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512dqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fp16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fp16vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512ifmaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512ifmavlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmi2intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmi2vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmiintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmivlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vlbwintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vldqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vnniintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vnnivlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vp2intersectintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vp2intersectvlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vpopcntdqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vpopcntdqvlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxifmaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxneconvertintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniint16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniint8intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/bmi2intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/bmiintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cetintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cldemoteintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clflushoptintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clwbintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clzerointrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cmpccxaddintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/emmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/enqcmdintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/f16cintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fma4intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fmaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fxsrintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/gfniintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/hresetintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/ia32intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/immintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/keylockerintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/lwpintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/lzcntintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm3dnow.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/movdirintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/movrsintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mwaitintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mwaitxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pconfigintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pkuintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/popcntintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/prfchiintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/prfchwintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/raointintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/rdseedintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/rtmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/serializeintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sgxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sha512intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/shaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sm3intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sm4intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/smmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tbmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tsxldtrkintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/uintrintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/usermsrintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/vaesintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/vpclmulqdqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/waitpkgintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/wbnoinvdintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/wmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/x86gprintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/x86intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xopintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsavecintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsaveintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsaveoptintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsavesintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xtestintrin.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/an-endian.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsfile.h \
  E:/github/astrometry.net_win/include/astrometry/fitsioutils.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/log.h \
  E:/github/astrometry.net_win/include/astrometry/os-features-config.h \
  E:/github/astrometry.net_win/include/astrometry/os-features.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj: E:/github/astrometry.net_win/util/index.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/time.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/codekd.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/healpix.h \
  E:/github/astrometry.net_win/include/astrometry/index.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/log.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_rw.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/quadfile.h \
  E:/github/astrometry.net_win/include/astrometry/starkd.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h \
  E:/github/astrometry.net_win/include/astrometry/tic.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj: E:/github/astrometry.net_win/util/indexset.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/codekd.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/index.h \
  E:/github/astrometry.net_win/include/astrometry/indexset.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/quadfile.h \
  E:/github/astrometry.net_win/include/astrometry/starkd.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj: E:/github/astrometry.net_win/util/matchfile.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/codekd.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitsioutils.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/index.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/matchfile.h \
  E:/github/astrometry.net_win/include/astrometry/matchobj.h \
  E:/github/astrometry.net_win/include/astrometry/mathutil.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/quadfile.h \
  E:/github/astrometry.net_win/include/astrometry/sip.h \
  E:/github/astrometry.net_win/include/astrometry/starkd.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj: E:/github/astrometry.net_win/util/matchobj.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/codekd.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/index.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/log.h \
  E:/github/astrometry.net_win/include/astrometry/matchobj.h \
  E:/github/astrometry.net_win/include/astrometry/os-features-config.h \
  E:/github/astrometry.net_win/include/astrometry/os-features.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/quadfile.h \
  E:/github/astrometry.net_win/include/astrometry/sip.h \
  E:/github/astrometry.net_win/include/astrometry/starkd.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h \
  E:/github/astrometry.net_win/include/astrometry/starxy.h \
  E:/github/astrometry.net_win/include/astrometry/verify.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj: E:/github/astrometry.net_win/util/multiindex.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/codekd.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/index.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/log.h \
  E:/github/astrometry.net_win/include/astrometry/multiindex.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/quadfile.h \
  E:/github/astrometry.net_win/include/astrometry/starkd.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj: E:/github/astrometry.net_win/util/quadfile.c \
  C:/msys64/mingw64/include/_bsd_types.h \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_mingw_unicode.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/apiset.h \
  C:/msys64/mingw64/include/apisetcconv.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/basetsd.h \
  C:/msys64/mingw64/include/bcrypt.h \
  C:/msys64/mingw64/include/bemapiset.h \
  C:/msys64/mingw64/include/cderr.h \
  C:/msys64/mingw64/include/cguid.h \
  C:/msys64/mingw64/include/combaseapi.h \
  C:/msys64/mingw64/include/commdlg.h \
  C:/msys64/mingw64/include/concurrencysal.h \
  C:/msys64/mingw64/include/consoleapi.h \
  C:/msys64/mingw64/include/consoleapi2.h \
  C:/msys64/mingw64/include/consoleapi3.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/ctype.h \
  C:/msys64/mingw64/include/datetimeapi.h \
  C:/msys64/mingw64/include/dde.h \
  C:/msys64/mingw64/include/ddeml.h \
  C:/msys64/mingw64/include/debugapi.h \
  C:/msys64/mingw64/include/dlgs.h \
  C:/msys64/mingw64/include/dpapi.h \
  C:/msys64/mingw64/include/driverspecs.h \
  C:/msys64/mingw64/include/errhandlingapi.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/excpt.h \
  C:/msys64/mingw64/include/fibersapi.h \
  C:/msys64/mingw64/include/fileapi.h \
  C:/msys64/mingw64/include/fltwinerror.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/guiddef.h \
  C:/msys64/mingw64/include/handleapi.h \
  C:/msys64/mingw64/include/heapapi.h \
  C:/msys64/mingw64/include/imm.h \
  C:/msys64/mingw64/include/inaddr.h \
  C:/msys64/mingw64/include/interlockedapi.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/ioapiset.h \
  C:/msys64/mingw64/include/jobapi.h \
  C:/msys64/mingw64/include/joystickapi.h \
  C:/msys64/mingw64/include/ktmtypes.h \
  C:/msys64/mingw64/include/libloaderapi.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/lzexpand.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/mciapi.h \
  C:/msys64/mingw64/include/mcx.h \
  C:/msys64/mingw64/include/memoryapi.h \
  C:/msys64/mingw64/include/minwinbase.h \
  C:/msys64/mingw64/include/minwindef.h \
  C:/msys64/mingw64/include/mmeapi.h \
  C:/msys64/mingw64/include/mmiscapi.h \
  C:/msys64/mingw64/include/mmiscapi2.h \
  C:/msys64/mingw64/include/mmsyscom.h \
  C:/msys64/mingw64/include/mmsystem.h \
  C:/msys64/mingw64/include/msxml.h \
  C:/msys64/mingw64/include/namedpipeapi.h \
  C:/msys64/mingw64/include/namespaceapi.h \
  C:/msys64/mingw64/include/nb30.h \
  C:/msys64/mingw64/include/ncrypt.h \
  C:/msys64/mingw64/include/oaidl.h \
  C:/msys64/mingw64/include/objbase.h \
  C:/msys64/mingw64/include/objidl.h \
  C:/msys64/mingw64/include/objidlbase.h \
  C:/msys64/mingw64/include/ole2.h \
  C:/msys64/mingw64/include/oleauto.h \
  C:/msys64/mingw64/include/oleidl.h \
  C:/msys64/mingw64/include/playsoundapi.h \
  C:/msys64/mingw64/include/poppack.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/processenv.h \
  C:/msys64/mingw64/include/processthreadsapi.h \
  C:/msys64/mingw64/include/processtopologyapi.h \
  C:/msys64/mingw64/include/profileapi.h \
  C:/msys64/mingw64/include/propidl.h \
  C:/msys64/mingw64/include/prsht.h \
  C:/msys64/mingw64/include/psdk_inc/_fd_types.h \
  C:/msys64/mingw64/include/psdk_inc/_ip_types.h \
  C:/msys64/mingw64/include/psdk_inc/_socket_types.h \
  C:/msys64/mingw64/include/psdk_inc/_ws1_undef.h \
  C:/msys64/mingw64/include/psdk_inc/_wsa_errnos.h \
  C:/msys64/mingw64/include/psdk_inc/_wsadata.h \
  C:/msys64/mingw64/include/psdk_inc/intrin-impl.h \
  C:/msys64/mingw64/include/pshpack1.h \
  C:/msys64/mingw64/include/pshpack2.h \
  C:/msys64/mingw64/include/pshpack4.h \
  C:/msys64/mingw64/include/pshpack8.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/qos.h \
  C:/msys64/mingw64/include/realtimeapiset.h \
  C:/msys64/mingw64/include/reason.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/rpc.h \
  C:/msys64/mingw64/include/rpcasync.h \
  C:/msys64/mingw64/include/rpcdce.h \
  C:/msys64/mingw64/include/rpcdcep.h \
  C:/msys64/mingw64/include/rpcndr.h \
  C:/msys64/mingw64/include/rpcnsi.h \
  C:/msys64/mingw64/include/rpcnsip.h \
  C:/msys64/mingw64/include/rpcnterr.h \
  C:/msys64/mingw64/include/rpcsal.h \
  C:/msys64/mingw64/include/sal.h \
  C:/msys64/mingw64/include/sdkddkver.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/stralign_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/securityappcontainer.h \
  C:/msys64/mingw64/include/securitybaseapi.h \
  C:/msys64/mingw64/include/servprov.h \
  C:/msys64/mingw64/include/shellapi.h \
  C:/msys64/mingw64/include/specstrings.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/stralign.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/stringapiset.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/synchapi.h \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/sysinfoapi.h \
  C:/msys64/mingw64/include/systemtopologyapi.h \
  C:/msys64/mingw64/include/threadpoolapiset.h \
  C:/msys64/mingw64/include/threadpoollegacyapiset.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/timeapi.h \
  C:/msys64/mingw64/include/timezoneapi.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/tvout.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/unknwn.h \
  C:/msys64/mingw64/include/unknwnbase.h \
  C:/msys64/mingw64/include/urlmon.h \
  C:/msys64/mingw64/include/utilapiset.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/virtdisk.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/include/winapifamily.h \
  C:/msys64/mingw64/include/winbase.h \
  C:/msys64/mingw64/include/wincon.h \
  C:/msys64/mingw64/include/wincontypes.h \
  C:/msys64/mingw64/include/wincrypt.h \
  C:/msys64/mingw64/include/windef.h \
  C:/msys64/mingw64/include/windows.h \
  C:/msys64/mingw64/include/winefs.h \
  C:/msys64/mingw64/include/winerror.h \
  C:/msys64/mingw64/include/wingdi.h \
  C:/msys64/mingw64/include/winioctl.h \
  C:/msys64/mingw64/include/winnetwk.h \
  C:/msys64/mingw64/include/winnls.h \
  C:/msys64/mingw64/include/winnt.h \
  C:/msys64/mingw64/include/winperf.h \
  C:/msys64/mingw64/include/winreg.h \
  C:/msys64/mingw64/include/winscard.h \
  C:/msys64/mingw64/include/winsmcrd.h \
  C:/msys64/mingw64/include/winsock.h \
  C:/msys64/mingw64/include/winsock2.h \
  C:/msys64/mingw64/include/winspool.h \
  C:/msys64/mingw64/include/winsvc.h \
  C:/msys64/mingw64/include/winuser.h \
  C:/msys64/mingw64/include/winver.h \
  C:/msys64/mingw64/include/wnnc.h \
  C:/msys64/mingw64/include/wow64apiset.h \
  C:/msys64/mingw64/include/ws2def.h \
  C:/msys64/mingw64/include/wtypes.h \
  C:/msys64/mingw64/include/wtypesbase.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/adxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/ammintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxavx512intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxbf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxcomplexintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxfp16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxfp8intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxint8intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxmovrsintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtf32intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtileintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtransposeintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512bf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512convertintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512mediaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512minmaxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512satcvtintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2bf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2convertintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2copyintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2mediaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2minmaxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2satcvtintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx2intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bf16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bf16vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bitalgintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bitalgvlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bwintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512cdintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512dqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fp16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fp16vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512ifmaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512ifmavlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmi2intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmi2vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmiintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmivlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vlbwintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vldqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vnniintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vnnivlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vp2intersectintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vp2intersectvlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vpopcntdqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vpopcntdqvlintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxifmaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxneconvertintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniint16intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniint8intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/bmi2intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/bmiintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cetintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cldemoteintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clflushoptintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clwbintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clzerointrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cmpccxaddintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/emmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/enqcmdintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/f16cintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fma4intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fmaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fxsrintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/gfniintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/hresetintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/ia32intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/immintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/keylockerintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/lwpintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/lzcntintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm3dnow.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/movdirintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/movrsintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mwaitintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mwaitxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pconfigintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pkuintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/popcntintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/prfchiintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/prfchwintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/raointintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/rdseedintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/rtmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/serializeintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sgxintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sha512intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/shaintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sm3intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sm4intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/smmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tbmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tsxldtrkintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/uintrintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/usermsrintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/vaesintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/vpclmulqdqintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/waitpkgintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/wbnoinvdintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/wmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/x86gprintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/x86intrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xmmintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xopintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsavecintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsaveintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsaveoptintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsavesintrin.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xtestintrin.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/an-endian.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitsioutils.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/quadfile.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj: E:/github/astrometry.net_win/util/rdlist.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/rdlist.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h \
  E:/github/astrometry.net_win/include/astrometry/starxy.h \
  E:/github/astrometry.net_win/include/astrometry/xylist.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj: E:/github/astrometry.net_win/util/scamp-catalog.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsioutils.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/log.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/scamp-catalog.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj: E:/github/astrometry.net_win/util/starkd.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stat64.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_startup.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wctype.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/getopt.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/process.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/pthread_unistd.h \
  C:/msys64/mingw64/include/regex.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/sec_api/wchar_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/time.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/tre/tre-config.h \
  C:/msys64/mingw64/include/tre/tre.h \
  C:/msys64/mingw64/include/unistd.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/include/wchar.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/anqfits.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/errors.h \
  E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
  E:/github/astrometry.net_win/include/astrometry/fitsioutils.h \
  E:/github/astrometry.net_win/include/astrometry/fitstable.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree.h \
  E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/log.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
  E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
  E:/github/astrometry.net_win/include/astrometry/starkd.h \
  E:/github/astrometry.net_win/include/astrometry/starutil.h \
  E:/github/astrometry.net_win/include/astrometry/tic.h

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj: E:/github/astrometry.net_win/util/starxy.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/_mingw_stdarg.h \
  C:/msys64/mingw64/include/_timeval.h \
  C:/msys64/mingw64/include/assert.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/pthread_compat.h \
  C:/msys64/mingw64/include/pthread_time.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
  C:/msys64/mingw64/include/stdarg.h \
  C:/msys64/mingw64/include/stddef.h \
  C:/msys64/mingw64/include/stdint.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/sys/param.h \
  C:/msys64/mingw64/include/sys/timeb.h \
  C:/msys64/mingw64/include/sys/types.h \
  C:/msys64/mingw64/include/time.h \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  E:/github/astrometry.net_win/include/astrometry/an-bool.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
  E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
  E:/github/astrometry.net_win/include/astrometry/bl.h \
  E:/github/astrometry.net_win/include/astrometry/ioutils.h \
  E:/github/astrometry.net_win/include/astrometry/keywords.h \
  E:/github/astrometry.net_win/include/astrometry/mathutil.h \
  E:/github/astrometry.net_win/include/astrometry/os-features-config.h \
  E:/github/astrometry.net_win/include/astrometry/os-features.h \
  E:/github/astrometry.net_win/include/astrometry/permutedsort.h \
  E:/github/astrometry.net_win/include/astrometry/starxy.h


C:/msys64/mingw64/include/corecrt.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2mediaintrin.h:

C:/msys64/mingw64/include/winefs.h:

E:/github/astrometry.net_win/util/codekd.c:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tmmintrin.h:

C:/msys64/mingw64/include/_mingw_stdarg.h:

E:/github/astrometry.net_win/include/astrometry/bl-nl.ph:

C:/msys64/mingw64/include/windef.h:

C:/msys64/mingw64/include/_mingw_secapi.h:

C:/msys64/mingw64/include/_mingw.h:

C:/msys64/mingw64/include/_mingw_mac.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/keylockerintrin.h:

C:/msys64/mingw64/include/rpcnterr.h:

C:/msys64/mingw64/include/corecrt_wstdlib.h:

C:/msys64/mingw64/include/combaseapi.h:

C:/msys64/mingw64/include/_mingw_off_t.h:

C:/msys64/mingw64/include/_mingw_stat64.h:

C:/msys64/mingw64/include/limits.h:

C:/msys64/mingw64/include/corecrt_startup.h:

C:/msys64/mingw64/include/unistd.h:

C:/msys64/mingw64/include/corecrt_stdio_config.h:

C:/msys64/mingw64/include/vadefs.h:

C:/msys64/mingw64/include/corecrt_wctype.h:

E:/github/astrometry.net_win/include/astrometry/starxy.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsaveoptintrin.h:

C:/msys64/mingw64/include/crtdefs.h:

C:/msys64/mingw64/include/errno.h:

C:/msys64/mingw64/include/rpcsal.h:

C:/msys64/mingw64/include/process.h:

C:/msys64/mingw64/include/getopt.h:

E:/github/astrometry.net_win/include/astrometry/qfits_table.h:

C:/msys64/mingw64/include/io.h:

E:/github/astrometry.net_win/include/astrometry/sip.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h:

C:/msys64/mingw64/include/objidl.h:

C:/msys64/mingw64/include/malloc.h:

C:/msys64/mingw64/include/math.h:

C:/msys64/mingw64/include/pthread_unistd.h:

E:/github/astrometry.net_win/include/astrometry/kdtree.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mwaitxintrin.h:

C:/msys64/mingw64/include/regex.h:

E:/github/astrometry.net_win/include/astrometry/qfits_image.h:

E:/github/astrometry.net_win/include/astrometry/an-endian.h:

C:/msys64/mingw64/include/rpcasync.h:

E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h:

C:/msys64/mingw64/include/pshpack1.h:

C:/msys64/mingw64/include/stdio.h:

C:/msys64/mingw64/include/sdks/_mingw_ddk.h:

C:/msys64/mingw64/include/psdk_inc/_socket_types.h:

E:/github/astrometry.net_win/include/astrometry/bl.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/raointintrin.h:

C:/msys64/mingw64/include/sec_api/stdio_s.h:

C:/msys64/mingw64/include/sec_api/stdlib_s.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2minmaxintrin.h:

C:/msys64/mingw64/include/interlockedapi.h:

C:/msys64/mingw64/include/sec_api/string_s.h:

C:/msys64/mingw64/include/winapifamily.h:

C:/msys64/mingw64/include/sec_api/wchar_s.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h:

C:/msys64/mingw64/include/sysinfoapi.h:

C:/msys64/mingw64/include/stdarg.h:

C:/msys64/mingw64/include/stddef.h:

E:/github/astrometry.net_win/include/astrometry/permutedsort.h:

C:/msys64/mingw64/include/stdint.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h:

C:/msys64/mingw64/include/ole2.h:

C:/msys64/mingw64/include/swprintf.inl:

C:/msys64/mingw64/include/stdlib.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clflushoptintrin.h:

E:/github/astrometry.net_win/include/astrometry/qfits_std.h:

C:/msys64/mingw64/include/string.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h:

C:/msys64/mingw64/include/sys/param.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vldqintrin.h:

C:/msys64/mingw64/include/sys/types.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fintrin.h:

C:/msys64/mingw64/include/winsock2.h:

C:/msys64/mingw64/include/basetsd.h:

C:/msys64/mingw64/include/tre/tre-config.h:

E:/github/astrometry.net_win/include/astrometry/errors.h:

C:/msys64/mingw64/include/playsoundapi.h:

C:/msys64/mingw64/include/tre/tre.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2satcvtintrin.h:

E:/github/astrometry.net_win/include/astrometry/qfits_header.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h:

C:/msys64/mingw64/include/wchar.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h:

C:/msys64/mingw64/include/winsock.h:

E:/github/astrometry.net_win/include/astrometry/an-bool.h:

E:/github/astrometry.net_win/include/astrometry/anqfits.h:

E:/github/astrometry.net_win/include/astrometry/bl-nl.h:

C:/msys64/mingw64/include/errhandlingapi.h:

E:/github/astrometry.net_win/include/astrometry/codekd.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vpopcntdqintrin.h:

E:/github/astrometry.net_win/include/astrometry/fitsbin.h:

E:/github/astrometry.net_win/util/indexset.c:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512bf16intrin.h:

E:/github/astrometry.net_win/include/astrometry/keywords.h:

C:/msys64/mingw64/include/inaddr.h:

E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h:

C:/msys64/mingw64/include/objidlbase.h:

E:/github/astrometry.net_win/include/astrometry/qfits_time.h:

E:/github/astrometry.net_win/include/astrometry/qfits_tools.h:

E:/github/astrometry.net_win/include/astrometry/starutil.h:

E:/github/astrometry.net_win/util/fitstable.c:

C:/msys64/mingw64/include/_bsd_types.h:

C:/msys64/mingw64/include/_mingw_unicode.h:

C:/msys64/mingw64/include/_timeval.h:

E:/github/astrometry.net_win/include/astrometry/indexset.h:

C:/msys64/mingw64/include/apiset.h:

C:/msys64/mingw64/include/apisetcconv.h:

C:/msys64/mingw64/include/assert.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxintrin.h:

C:/msys64/mingw64/include/bcrypt.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmiintrin.h:

C:/msys64/mingw64/include/stralign.h:

C:/msys64/mingw64/include/pthread_time.h:

C:/msys64/mingw64/include/bemapiset.h:

C:/msys64/mingw64/include/sdkddkver.h:

C:/msys64/mingw64/include/cderr.h:

C:/msys64/mingw64/include/cguid.h:

C:/msys64/mingw64/include/commdlg.h:

C:/msys64/mingw64/include/winsmcrd.h:

C:/msys64/mingw64/include/concurrencysal.h:

C:/msys64/mingw64/include/namedpipeapi.h:

C:/msys64/mingw64/include/consoleapi.h:

C:/msys64/mingw64/include/consoleapi2.h:

C:/msys64/mingw64/include/consoleapi3.h:

C:/msys64/mingw64/include/ctype.h:

C:/msys64/mingw64/include/datetimeapi.h:

C:/msys64/mingw64/include/dde.h:

C:/msys64/mingw64/include/ddeml.h:

C:/msys64/mingw64/include/debugapi.h:

C:/msys64/mingw64/include/dlgs.h:

C:/msys64/mingw64/include/dpapi.h:

C:/msys64/mingw64/include/driverspecs.h:

C:/msys64/mingw64/include/excpt.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vp2intersectvlintrin.h:

C:/msys64/mingw64/include/winscard.h:

C:/msys64/mingw64/include/fibersapi.h:

C:/msys64/mingw64/include/pshpack8.h:

C:/msys64/mingw64/include/fileapi.h:

C:/msys64/mingw64/include/fltwinerror.h:

C:/msys64/mingw64/include/guiddef.h:

C:/msys64/mingw64/include/handleapi.h:

C:/msys64/mingw64/include/heapapi.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2bf16intrin.h:

C:/msys64/mingw64/include/winspool.h:

C:/msys64/mingw64/include/namespaceapi.h:

C:/msys64/mingw64/include/imm.h:

C:/msys64/mingw64/include/ioapiset.h:

C:/msys64/mingw64/include/jobapi.h:

C:/msys64/mingw64/include/joystickapi.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/f16cintrin.h:

C:/msys64/mingw64/include/ktmtypes.h:

C:/msys64/mingw64/include/mcx.h:

C:/msys64/mingw64/include/libloaderapi.h:

C:/msys64/mingw64/include/lzexpand.h:

C:/msys64/mingw64/include/threadpoollegacyapiset.h:

C:/msys64/mingw64/include/mciapi.h:

C:/msys64/mingw64/include/memoryapi.h:

C:/msys64/mingw64/include/minwinbase.h:

E:/github/astrometry.net_win/include/astrometry/verify.h:

C:/msys64/mingw64/include/minwindef.h:

C:/msys64/mingw64/include/mmeapi.h:

C:/msys64/mingw64/include/mmiscapi.h:

C:/msys64/mingw64/include/securitybaseapi.h:

C:/msys64/mingw64/include/mmiscapi2.h:

C:/msys64/mingw64/include/mmsyscom.h:

C:/msys64/mingw64/include/mmsystem.h:

C:/msys64/mingw64/include/windows.h:

C:/msys64/mingw64/include/msxml.h:

C:/msys64/mingw64/include/rpc.h:

C:/msys64/mingw64/include/psdk_inc/_ws1_undef.h:

C:/msys64/mingw64/include/nb30.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sm3intrin.h:

C:/msys64/mingw64/include/ncrypt.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/prfchiintrin.h:

C:/msys64/mingw64/include/oaidl.h:

C:/msys64/mingw64/include/objbase.h:

C:/msys64/mingw64/include/oleauto.h:

C:/msys64/mingw64/include/oleidl.h:

C:/msys64/mingw64/include/poppack.h:

C:/msys64/mingw64/include/processenv.h:

C:/msys64/mingw64/include/processthreadsapi.h:

C:/msys64/mingw64/include/processtopologyapi.h:

C:/msys64/mingw64/include/profileapi.h:

C:/msys64/mingw64/include/propidl.h:

C:/msys64/mingw64/include/prsht.h:

C:/msys64/mingw64/include/psdk_inc/_fd_types.h:

C:/msys64/mingw64/include/psdk_inc/_ip_types.h:

C:/msys64/mingw64/include/psdk_inc/_wsa_errnos.h:

C:/msys64/mingw64/include/psdk_inc/_wsadata.h:

C:/msys64/mingw64/include/psdk_inc/intrin-impl.h:

C:/msys64/mingw64/include/pshpack2.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/vpclmulqdqintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/rtmintrin.h:

C:/msys64/mingw64/include/pshpack4.h:

C:/msys64/mingw64/include/pthread_compat.h:

E:/github/astrometry.net_win/util/quadfile.c:

C:/msys64/mingw64/include/qos.h:

C:/msys64/mingw64/include/realtimeapiset.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vlintrin.h:

C:/msys64/mingw64/include/reason.h:

C:/msys64/mingw64/include/rpcdce.h:

C:/msys64/mingw64/include/rpcdcep.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512mediaintrin.h:

C:/msys64/mingw64/include/rpcndr.h:

C:/msys64/mingw64/include/rpcnsi.h:

C:/msys64/mingw64/include/rpcnsip.h:

C:/msys64/mingw64/include/sal.h:

C:/msys64/mingw64/include/sec_api/stralign_s.h:

C:/msys64/mingw64/include/sec_api/sys/timeb_s.h:

C:/msys64/mingw64/include/securityappcontainer.h:

C:/msys64/mingw64/include/servprov.h:

C:/msys64/mingw64/include/wincrypt.h:

C:/msys64/mingw64/include/shellapi.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxifmaintrin.h:

C:/msys64/mingw64/include/specstrings.h:

C:/msys64/mingw64/include/stringapiset.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/bmiintrin.h:

C:/msys64/mingw64/include/synchapi.h:

C:/msys64/mingw64/include/sys/timeb.h:

C:/msys64/mingw64/include/systemtopologyapi.h:

C:/msys64/mingw64/include/threadpoolapiset.h:

C:/msys64/mingw64/include/time.h:

E:/github/astrometry.net_win/include/astrometry/matchfile.h:

C:/msys64/mingw64/include/timeapi.h:

C:/msys64/mingw64/include/timezoneapi.h:

C:/msys64/mingw64/include/tvout.h:

C:/msys64/mingw64/include/unknwn.h:

C:/msys64/mingw64/include/unknwnbase.h:

C:/msys64/mingw64/include/urlmon.h:

C:/msys64/mingw64/include/utilapiset.h:

C:/msys64/mingw64/include/virtdisk.h:

C:/msys64/mingw64/include/sys/time.h:

C:/msys64/mingw64/include/winbase.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxbf16intrin.h:

C:/msys64/mingw64/include/wincon.h:

C:/msys64/mingw64/include/wincontypes.h:

C:/msys64/mingw64/include/winerror.h:

C:/msys64/mingw64/include/wingdi.h:

C:/msys64/mingw64/include/winioctl.h:

C:/msys64/mingw64/include/winnetwk.h:

C:/msys64/mingw64/include/winnls.h:

E:/github/astrometry.net_win/include/astrometry/fitstable.h:

C:/msys64/mingw64/include/winnt.h:

C:/msys64/mingw64/include/winperf.h:

C:/msys64/mingw64/include/winreg.h:

C:/msys64/mingw64/include/winsvc.h:

C:/msys64/mingw64/include/winuser.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vnniintrin.h:

C:/msys64/mingw64/include/winver.h:

E:/github/astrometry.net_win/util/matchobj.c:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/immintrin.h:

C:/msys64/mingw64/include/wnnc.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/gfniintrin.h:

C:/msys64/mingw64/include/wow64apiset.h:

C:/msys64/mingw64/include/ws2def.h:

C:/msys64/mingw64/include/wtypes.h:

C:/msys64/mingw64/include/wtypesbase.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/adxintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/ammintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cldemoteintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxavx512intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxcomplexintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxfp16intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512dqintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2convertintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxfp8intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxint8intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxmovrsintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtf32intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtileintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/amxtransposeintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512convertintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512minmaxintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2-512satcvtintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx10_2copyintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx2intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bf16intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bf16vlintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bitalgintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bitalgvlintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512bwintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512cdintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fp16intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512fp16vlintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512ifmaintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512ifmavlintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmi2intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmi2vlintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vbmivlintrin.h:

E:/github/astrometry.net_win/include/astrometry/xylist.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vlbwintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vnnivlintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vp2intersectintrin.h:

E:/github/astrometry.net_win/include/astrometry/qfits_rw.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avx512vpopcntdqvlintrin.h:

E:/github/astrometry.net_win/include/astrometry/mathutil.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxneconvertintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniint16intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniint8intrin.h:

E:/github/astrometry.net_win/util/starkd.c:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/avxvnniintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/bmi2intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cetintrin.h:

E:/github/astrometry.net_win/include/astrometry/os-features-config.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clwbintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/clzerointrin.h:

E:/github/astrometry.net_win/include/astrometry/scamp-catalog.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/cmpccxaddintrin.h:

E:/github/astrometry.net_win/include/astrometry/log.h:

E:/github/astrometry.net_win/include/astrometry/fitsfile.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/emmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/enqcmdintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pmmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fma4intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fmaintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/fxsrintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/hresetintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/ia32intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/vaesintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/lwpintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/lzcntintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm3dnow.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/movdirintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/movrsintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mwaitintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pconfigintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sha512intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/pkuintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/popcntintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/prfchwintrin.h:

E:/github/astrometry.net_win/include/astrometry/matchobj.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/rdseedintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/serializeintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sgxintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/shaintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/sm4intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/smmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tbmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/tsxldtrkintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/uintrintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/wmmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/usermsrintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/waitpkgintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/wbnoinvdintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/x86gprintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/x86intrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xmmintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xopintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsavecintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsaveintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xsavesintrin.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/xtestintrin.h:

E:/github/astrometry.net_win/include/astrometry/fitsioutils.h:

E:/github/astrometry.net_win/util/matchfile.c:

E:/github/astrometry.net_win/include/astrometry/quadfile.h:

E:/github/astrometry.net_win/include/astrometry/ioutils.h:

E:/github/astrometry.net_win/include/astrometry/os-features.h:

E:/github/astrometry.net_win/util/index.c:

E:/github/astrometry.net_win/include/astrometry/healpix.h:

E:/github/astrometry.net_win/include/astrometry/index.h:

E:/github/astrometry.net_win/include/astrometry/starkd.h:

E:/github/astrometry.net_win/include/astrometry/tic.h:

E:/github/astrometry.net_win/util/starxy.c:

E:/github/astrometry.net_win/util/multiindex.c:

E:/github/astrometry.net_win/include/astrometry/multiindex.h:

E:/github/astrometry.net_win/util/rdlist.c:

E:/github/astrometry.net_win/include/astrometry/rdlist.h:

E:/github/astrometry.net_win/util/scamp-catalog.c:
