
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/util/codekd.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj.d"
  "E:/github/astrometry.net_win/util/fitstable.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj.d"
  "E:/github/astrometry.net_win/util/index.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj.d"
  "E:/github/astrometry.net_win/util/indexset.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj.d"
  "E:/github/astrometry.net_win/util/matchfile.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj.d"
  "E:/github/astrometry.net_win/util/matchobj.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj.d"
  "E:/github/astrometry.net_win/util/multiindex.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj.d"
  "E:/github/astrometry.net_win/util/quadfile.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj.d"
  "E:/github/astrometry.net_win/util/rdlist.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj.d"
  "E:/github/astrometry.net_win/util/scamp-catalog.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj.d"
  "E:/github/astrometry.net_win/util/starkd.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj.d"
  "E:/github/astrometry.net_win/util/starxy.c" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj" "gcc" "CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
