CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj: \
 E:/github/astrometry.net_win/util/starxy.c \
 C:/msys64/mingw64/include/assert.h C:/msys64/mingw64/include/crtdefs.h \
 C:/msys64/mingw64/include/corecrt.h C:/msys64/mingw64/include/_mingw.h \
 C:/msys64/mingw64/include/_mingw_mac.h \
 C:/msys64/mingw64/include/_mingw_secapi.h \
 C:/msys64/mingw64/include/vadefs.h \
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 C:/msys64/mingw64/include/string.h \
 C:/msys64/mingw64/include/sec_api/string_s.h \
 C:/msys64/mingw64/include/math.h \
 E:/github/astrometry.net_win/include/astrometry/os-features.h \
 E:/github/astrometry.net_win/include/astrometry/os-features-config.h \
 C:/msys64/mingw64/include/sys/param.h \
 C:/msys64/mingw64/include/sys/types.h \
 C:/msys64/mingw64/include/_mingw_off_t.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
 C:/msys64/mingw64/include/limits.h \
 E:/github/astrometry.net_win/include/astrometry/starxy.h \
 E:/github/astrometry.net_win/include/astrometry/bl.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
 C:/msys64/mingw64/include/stddef.h C:/msys64/mingw64/include/stdlib.h \
 C:/msys64/mingw64/include/corecrt_wstdlib.h \
 C:/msys64/mingw64/include/sec_api/stdlib_s.h \
 C:/msys64/mingw64/include/malloc.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
 C:/msys64/mingw64/include/errno.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
 C:/msys64/mingw64/include/stdarg.h \
 C:/msys64/mingw64/include/_mingw_stdarg.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
 C:/msys64/mingw64/include/stdint.h \
 E:/github/astrometry.net_win/include/astrometry/keywords.h \
 E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
 E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
 E:/github/astrometry.net_win/include/astrometry/an-bool.h \
 E:/github/astrometry.net_win/include/astrometry/permutedsort.h \
 E:/github/astrometry.net_win/include/astrometry/ioutils.h \
 C:/msys64/mingw64/include/stdio.h \
 C:/msys64/mingw64/include/corecrt_stdio_config.h \
 C:/msys64/mingw64/include/swprintf.inl \
 C:/msys64/mingw64/include/sec_api/stdio_s.h \
 C:/msys64/mingw64/include/time.h C:/msys64/mingw64/include/sys/timeb.h \
 C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
 C:/msys64/mingw64/include/_timeval.h \
 C:/msys64/mingw64/include/pthread_time.h \
 C:/msys64/mingw64/include/pthread_compat.h \
 E:/github/astrometry.net_win/include/astrometry/mathutil.h \
 E:/github/astrometry.net_win/include/astrometry/bl.h
