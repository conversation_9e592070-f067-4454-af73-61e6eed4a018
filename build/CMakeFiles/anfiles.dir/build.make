# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/anfiles.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/anfiles.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/anfiles.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/anfiles.dir/flags.make

CMakeFiles/anfiles.dir/codegen:
.PHONY : CMakeFiles/anfiles.dir/codegen

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj: E:/github/astrometry.net_win/util/index.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj -c /E/github/astrometry.net_win/util/index.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/index.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/index.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj: E:/github/astrometry.net_win/util/indexset.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj -c /E/github/astrometry.net_win/util/indexset.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/indexset.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/indexset.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj: E:/github/astrometry.net_win/util/multiindex.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj -c /E/github/astrometry.net_win/util/multiindex.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/multiindex.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/multiindex.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj: E:/github/astrometry.net_win/util/codekd.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj -c /E/github/astrometry.net_win/util/codekd.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/codekd.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/codekd.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj: E:/github/astrometry.net_win/util/quadfile.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj -c /E/github/astrometry.net_win/util/quadfile.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/quadfile.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/quadfile.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj: E:/github/astrometry.net_win/util/matchfile.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj -c /E/github/astrometry.net_win/util/matchfile.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/matchfile.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/matchfile.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj: E:/github/astrometry.net_win/util/matchobj.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj -c /E/github/astrometry.net_win/util/matchobj.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/matchobj.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/matchobj.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj: E:/github/astrometry.net_win/util/rdlist.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj -c /E/github/astrometry.net_win/util/rdlist.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/rdlist.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/rdlist.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj: E:/github/astrometry.net_win/util/starkd.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj -c /E/github/astrometry.net_win/util/starkd.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/starkd.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/starkd.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj: E:/github/astrometry.net_win/util/starxy.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj -c /E/github/astrometry.net_win/util/starxy.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/starxy.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/starxy.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj: E:/github/astrometry.net_win/util/scamp-catalog.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj -c /E/github/astrometry.net_win/util/scamp-catalog.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/scamp-catalog.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/scamp-catalog.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.s

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj: CMakeFiles/anfiles.dir/flags.make
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj: E:/github/astrometry.net_win/util/fitstable.c
CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj: CMakeFiles/anfiles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj -MF CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj.d -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj -c /E/github/astrometry.net_win/util/fitstable.c

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/fitstable.c > CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.i

CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/fitstable.c -o CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.s

# Object files for target anfiles
anfiles_OBJECTS = \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj" \
"CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj"

# External object files for target anfiles
anfiles_EXTERNAL_OBJECTS =

lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/index.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/indexset.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/multiindex.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/codekd.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/quadfile.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchfile.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/matchobj.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/rdlist.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starkd.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/starxy.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/scamp-catalog.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/E_/github/astrometry.net_win/util/fitstable.c.obj
lib/libanfiles.a: CMakeFiles/anfiles.dir/build.make
lib/libanfiles.a: CMakeFiles/anfiles.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Linking C static library lib/libanfiles.a"
	$(CMAKE_COMMAND) -P CMakeFiles/anfiles.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/anfiles.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/anfiles.dir/build: lib/libanfiles.a
.PHONY : CMakeFiles/anfiles.dir/build

CMakeFiles/anfiles.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/anfiles.dir/cmake_clean.cmake
.PHONY : CMakeFiles/anfiles.dir/clean

CMakeFiles/anfiles.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/anfiles.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/anfiles.dir/depend

