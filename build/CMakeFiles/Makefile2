# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/qfits.dir/all
all: CMakeFiles/anbase.dir/all
all: CMakeFiles/anutils.dir/all
all: CMakeFiles/anfiles.dir/all
all: CMakeFiles/libkd.dir/all
all: CMakeFiles/catalogs.dir/all
all: CMakeFiles/astrometry.dir/all
all: CMakeFiles/solve-field.dir/all
all: CMakeFiles/astrometry-engine.dir/all
all: CMakeFiles/test-find-executable.dir/all
all: CMakeFiles/an-fitstopnm.dir/all
all: CMakeFiles/an-pnmtofits.dir/all
all: CMakeFiles/image2pnm.dir/all
all: CMakeFiles/removelines.dir/all
all: CMakeFiles/uniformize.dir/all
all: CMakeFiles/test_fits_read.dir/all
all: CMakeFiles/debug_fits.dir/all
all: CMakeFiles/debug_cfitsio.dir/all
all: CMakeFiles/debug_qfits_detailed.dir/all
all: CMakeFiles/copy_dlls.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/qfits.dir/codegen
codegen: CMakeFiles/anbase.dir/codegen
codegen: CMakeFiles/anutils.dir/codegen
codegen: CMakeFiles/anfiles.dir/codegen
codegen: CMakeFiles/libkd.dir/codegen
codegen: CMakeFiles/catalogs.dir/codegen
codegen: CMakeFiles/astrometry.dir/codegen
codegen: CMakeFiles/solve-field.dir/codegen
codegen: CMakeFiles/astrometry-engine.dir/codegen
codegen: CMakeFiles/test-find-executable.dir/codegen
codegen: CMakeFiles/an-fitstopnm.dir/codegen
codegen: CMakeFiles/an-pnmtofits.dir/codegen
codegen: CMakeFiles/image2pnm.dir/codegen
codegen: CMakeFiles/removelines.dir/codegen
codegen: CMakeFiles/uniformize.dir/codegen
codegen: CMakeFiles/test_fits_read.dir/codegen
codegen: CMakeFiles/debug_fits.dir/codegen
codegen: CMakeFiles/debug_cfitsio.dir/codegen
codegen: CMakeFiles/debug_qfits_detailed.dir/codegen
codegen: CMakeFiles/copy_dlls.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/qfits.dir/clean
clean: CMakeFiles/anbase.dir/clean
clean: CMakeFiles/anutils.dir/clean
clean: CMakeFiles/anfiles.dir/clean
clean: CMakeFiles/libkd.dir/clean
clean: CMakeFiles/catalogs.dir/clean
clean: CMakeFiles/astrometry.dir/clean
clean: CMakeFiles/solve-field.dir/clean
clean: CMakeFiles/astrometry-engine.dir/clean
clean: CMakeFiles/test-find-executable.dir/clean
clean: CMakeFiles/an-fitstopnm.dir/clean
clean: CMakeFiles/an-pnmtofits.dir/clean
clean: CMakeFiles/image2pnm.dir/clean
clean: CMakeFiles/removelines.dir/clean
clean: CMakeFiles/uniformize.dir/clean
clean: CMakeFiles/test_fits_read.dir/clean
clean: CMakeFiles/debug_fits.dir/clean
clean: CMakeFiles/debug_cfitsio.dir/clean
clean: CMakeFiles/debug_qfits_detailed.dir/clean
clean: CMakeFiles/copy_dlls.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/qfits.dir

# All Build rule for target.
CMakeFiles/qfits.dir/all: CMakeFiles/anbase.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=83,84,85,86,87,88,89,90,91,92,93 "Built target qfits"
.PHONY : CMakeFiles/qfits.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/qfits.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/qfits.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/qfits.dir/rule

# Convenience name for target.
qfits: CMakeFiles/qfits.dir/rule
.PHONY : qfits

# codegen rule for target.
CMakeFiles/qfits.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=83,84,85,86,87,88,89,90,91,92,93 "Finished codegen for target qfits"
.PHONY : CMakeFiles/qfits.dir/codegen

# clean rule for target.
CMakeFiles/qfits.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qfits.dir/build.make CMakeFiles/qfits.dir/clean
.PHONY : CMakeFiles/qfits.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/anbase.dir

# All Build rule for target.
CMakeFiles/anbase.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=3,4,5,6,7,8,9,10 "Built target anbase"
.PHONY : CMakeFiles/anbase.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/anbase.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/anbase.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/anbase.dir/rule

# Convenience name for target.
anbase: CMakeFiles/anbase.dir/rule
.PHONY : anbase

# codegen rule for target.
CMakeFiles/anbase.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=3,4,5,6,7,8,9,10 "Finished codegen for target anbase"
.PHONY : CMakeFiles/anbase.dir/codegen

# clean rule for target.
CMakeFiles/anbase.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anbase.dir/build.make CMakeFiles/anbase.dir/clean
.PHONY : CMakeFiles/anbase.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/anutils.dir

# All Build rule for target.
CMakeFiles/anutils.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/anutils.dir/all: CMakeFiles/qfits.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41 "Built target anutils"
.PHONY : CMakeFiles/anutils.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/anutils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 41
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/anutils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/anutils.dir/rule

# Convenience name for target.
anutils: CMakeFiles/anutils.dir/rule
.PHONY : anutils

# codegen rule for target.
CMakeFiles/anutils.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41 "Finished codegen for target anutils"
.PHONY : CMakeFiles/anutils.dir/codegen

# clean rule for target.
CMakeFiles/anutils.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anutils.dir/build.make CMakeFiles/anutils.dir/clean
.PHONY : CMakeFiles/anutils.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/anfiles.dir

# All Build rule for target.
CMakeFiles/anfiles.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/anfiles.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/anfiles.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/anfiles.dir/all: CMakeFiles/anutils.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19 "Built target anfiles"
.PHONY : CMakeFiles/anfiles.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/anfiles.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 58
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/anfiles.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/anfiles.dir/rule

# Convenience name for target.
anfiles: CMakeFiles/anfiles.dir/rule
.PHONY : anfiles

# codegen rule for target.
CMakeFiles/anfiles.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19 "Finished codegen for target anfiles"
.PHONY : CMakeFiles/anfiles.dir/codegen

# clean rule for target.
CMakeFiles/anfiles.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/anfiles.dir/build.make CMakeFiles/anfiles.dir/clean
.PHONY : CMakeFiles/anfiles.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/libkd.dir

# All Build rule for target.
CMakeFiles/libkd.dir/all: CMakeFiles/anbase.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=75,76,77,78,79,80,81,82 "Built target libkd"
.PHONY : CMakeFiles/libkd.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/libkd.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/libkd.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/libkd.dir/rule

# Convenience name for target.
libkd: CMakeFiles/libkd.dir/rule
.PHONY : libkd

# codegen rule for target.
CMakeFiles/libkd.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=75,76,77,78,79,80,81,82 "Finished codegen for target libkd"
.PHONY : CMakeFiles/libkd.dir/codegen

# clean rule for target.
CMakeFiles/libkd.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkd.dir/build.make CMakeFiles/libkd.dir/clean
.PHONY : CMakeFiles/libkd.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/catalogs.dir

# All Build rule for target.
CMakeFiles/catalogs.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/catalogs.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/catalogs.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/catalogs.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/catalogs.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=61,62,63,64,65,66,67,68 "Built target catalogs"
.PHONY : CMakeFiles/catalogs.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/catalogs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 66
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/catalogs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/catalogs.dir/rule

# Convenience name for target.
catalogs: CMakeFiles/catalogs.dir/rule
.PHONY : catalogs

# codegen rule for target.
CMakeFiles/catalogs.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=61,62,63,64,65,66,67,68 "Finished codegen for target catalogs"
.PHONY : CMakeFiles/catalogs.dir/codegen

# clean rule for target.
CMakeFiles/catalogs.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/catalogs.dir/build.make CMakeFiles/catalogs.dir/clean
.PHONY : CMakeFiles/catalogs.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/astrometry.dir

# All Build rule for target.
CMakeFiles/astrometry.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/astrometry.dir/all: CMakeFiles/catalogs.dir/all
CMakeFiles/astrometry.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/astrometry.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/astrometry.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/astrometry.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58 "Built target astrometry"
.PHONY : CMakeFiles/astrometry.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/astrometry.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 83
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/astrometry.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/astrometry.dir/rule

# Convenience name for target.
astrometry: CMakeFiles/astrometry.dir/rule
.PHONY : astrometry

# codegen rule for target.
CMakeFiles/astrometry.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58 "Finished codegen for target astrometry"
.PHONY : CMakeFiles/astrometry.dir/codegen

# clean rule for target.
CMakeFiles/astrometry.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry.dir/build.make CMakeFiles/astrometry.dir/clean
.PHONY : CMakeFiles/astrometry.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/solve-field.dir

# All Build rule for target.
CMakeFiles/solve-field.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/solve-field.dir/all: CMakeFiles/catalogs.dir/all
CMakeFiles/solve-field.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/solve-field.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/solve-field.dir/all: CMakeFiles/astrometry.dir/all
CMakeFiles/solve-field.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/solve-field.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=95,96 "Built target solve-field"
.PHONY : CMakeFiles/solve-field.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/solve-field.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 85
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/solve-field.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/solve-field.dir/rule

# Convenience name for target.
solve-field: CMakeFiles/solve-field.dir/rule
.PHONY : solve-field

# codegen rule for target.
CMakeFiles/solve-field.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=95,96 "Finished codegen for target solve-field"
.PHONY : CMakeFiles/solve-field.dir/codegen

# clean rule for target.
CMakeFiles/solve-field.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/solve-field.dir/build.make CMakeFiles/solve-field.dir/clean
.PHONY : CMakeFiles/solve-field.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/astrometry-engine.dir

# All Build rule for target.
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/catalogs.dir/all
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/astrometry.dir/all
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/astrometry-engine.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=59,60 "Built target astrometry-engine"
.PHONY : CMakeFiles/astrometry-engine.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/astrometry-engine.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 85
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/astrometry-engine.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/astrometry-engine.dir/rule

# Convenience name for target.
astrometry-engine: CMakeFiles/astrometry-engine.dir/rule
.PHONY : astrometry-engine

# codegen rule for target.
CMakeFiles/astrometry-engine.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=59,60 "Finished codegen for target astrometry-engine"
.PHONY : CMakeFiles/astrometry-engine.dir/codegen

# clean rule for target.
CMakeFiles/astrometry-engine.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/astrometry-engine.dir/build.make CMakeFiles/astrometry-engine.dir/clean
.PHONY : CMakeFiles/astrometry-engine.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test-find-executable.dir

# All Build rule for target.
CMakeFiles/test-find-executable.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/test-find-executable.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/test-find-executable.dir/all: CMakeFiles/anutils.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=97 "Built target test-find-executable"
.PHONY : CMakeFiles/test-find-executable.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test-find-executable.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 42
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test-find-executable.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/test-find-executable.dir/rule

# Convenience name for target.
test-find-executable: CMakeFiles/test-find-executable.dir/rule
.PHONY : test-find-executable

# codegen rule for target.
CMakeFiles/test-find-executable.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=97 "Finished codegen for target test-find-executable"
.PHONY : CMakeFiles/test-find-executable.dir/codegen

# clean rule for target.
CMakeFiles/test-find-executable.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test-find-executable.dir/build.make CMakeFiles/test-find-executable.dir/clean
.PHONY : CMakeFiles/test-find-executable.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/an-fitstopnm.dir

# All Build rule for target.
CMakeFiles/an-fitstopnm.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/an-fitstopnm.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/an-fitstopnm.dir/all: CMakeFiles/anutils.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=1 "Built target an-fitstopnm"
.PHONY : CMakeFiles/an-fitstopnm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/an-fitstopnm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 42
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/an-fitstopnm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/an-fitstopnm.dir/rule

# Convenience name for target.
an-fitstopnm: CMakeFiles/an-fitstopnm.dir/rule
.PHONY : an-fitstopnm

# codegen rule for target.
CMakeFiles/an-fitstopnm.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=1 "Finished codegen for target an-fitstopnm"
.PHONY : CMakeFiles/an-fitstopnm.dir/codegen

# clean rule for target.
CMakeFiles/an-fitstopnm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-fitstopnm.dir/build.make CMakeFiles/an-fitstopnm.dir/clean
.PHONY : CMakeFiles/an-fitstopnm.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/an-pnmtofits.dir

# All Build rule for target.
CMakeFiles/an-pnmtofits.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/an-pnmtofits.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/an-pnmtofits.dir/all: CMakeFiles/anutils.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=2 "Built target an-pnmtofits"
.PHONY : CMakeFiles/an-pnmtofits.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/an-pnmtofits.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 42
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/an-pnmtofits.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/an-pnmtofits.dir/rule

# Convenience name for target.
an-pnmtofits: CMakeFiles/an-pnmtofits.dir/rule
.PHONY : an-pnmtofits

# codegen rule for target.
CMakeFiles/an-pnmtofits.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=2 "Finished codegen for target an-pnmtofits"
.PHONY : CMakeFiles/an-pnmtofits.dir/codegen

# clean rule for target.
CMakeFiles/an-pnmtofits.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/an-pnmtofits.dir/build.make CMakeFiles/an-pnmtofits.dir/clean
.PHONY : CMakeFiles/an-pnmtofits.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/image2pnm.dir

# All Build rule for target.
CMakeFiles/image2pnm.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=74 "Built target image2pnm"
.PHONY : CMakeFiles/image2pnm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/image2pnm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/image2pnm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/image2pnm.dir/rule

# Convenience name for target.
image2pnm: CMakeFiles/image2pnm.dir/rule
.PHONY : image2pnm

# codegen rule for target.
CMakeFiles/image2pnm.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=74 "Finished codegen for target image2pnm"
.PHONY : CMakeFiles/image2pnm.dir/codegen

# clean rule for target.
CMakeFiles/image2pnm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/image2pnm.dir/build.make CMakeFiles/image2pnm.dir/clean
.PHONY : CMakeFiles/image2pnm.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/removelines.dir

# All Build rule for target.
CMakeFiles/removelines.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/removelines.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/removelines.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/removelines.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/removelines.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=94 "Built target removelines"
.PHONY : CMakeFiles/removelines.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/removelines.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 59
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/removelines.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/removelines.dir/rule

# Convenience name for target.
removelines: CMakeFiles/removelines.dir/rule
.PHONY : removelines

# codegen rule for target.
CMakeFiles/removelines.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=94 "Finished codegen for target removelines"
.PHONY : CMakeFiles/removelines.dir/codegen

# clean rule for target.
CMakeFiles/removelines.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/removelines.dir/build.make CMakeFiles/removelines.dir/clean
.PHONY : CMakeFiles/removelines.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/uniformize.dir

# All Build rule for target.
CMakeFiles/uniformize.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/uniformize.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/uniformize.dir/all: CMakeFiles/anutils.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=99,100 "Built target uniformize"
.PHONY : CMakeFiles/uniformize.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uniformize.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 43
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uniformize.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/uniformize.dir/rule

# Convenience name for target.
uniformize: CMakeFiles/uniformize.dir/rule
.PHONY : uniformize

# codegen rule for target.
CMakeFiles/uniformize.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=99,100 "Finished codegen for target uniformize"
.PHONY : CMakeFiles/uniformize.dir/codegen

# clean rule for target.
CMakeFiles/uniformize.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uniformize.dir/build.make CMakeFiles/uniformize.dir/clean
.PHONY : CMakeFiles/uniformize.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_fits_read.dir

# All Build rule for target.
CMakeFiles/test_fits_read.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/test_fits_read.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/test_fits_read.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/test_fits_read.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/test_fits_read.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=98 "Built target test_fits_read"
.PHONY : CMakeFiles/test_fits_read.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_fits_read.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 59
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_fits_read.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/test_fits_read.dir/rule

# Convenience name for target.
test_fits_read: CMakeFiles/test_fits_read.dir/rule
.PHONY : test_fits_read

# codegen rule for target.
CMakeFiles/test_fits_read.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=98 "Finished codegen for target test_fits_read"
.PHONY : CMakeFiles/test_fits_read.dir/codegen

# clean rule for target.
CMakeFiles/test_fits_read.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fits_read.dir/build.make CMakeFiles/test_fits_read.dir/clean
.PHONY : CMakeFiles/test_fits_read.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug_fits.dir

# All Build rule for target.
CMakeFiles/debug_fits.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/debug_fits.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/debug_fits.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/debug_fits.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/debug_fits.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=71,72 "Built target debug_fits"
.PHONY : CMakeFiles/debug_fits.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug_fits.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 60
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/debug_fits.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/debug_fits.dir/rule

# Convenience name for target.
debug_fits: CMakeFiles/debug_fits.dir/rule
.PHONY : debug_fits

# codegen rule for target.
CMakeFiles/debug_fits.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=71,72 "Finished codegen for target debug_fits"
.PHONY : CMakeFiles/debug_fits.dir/codegen

# clean rule for target.
CMakeFiles/debug_fits.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_fits.dir/build.make CMakeFiles/debug_fits.dir/clean
.PHONY : CMakeFiles/debug_fits.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug_cfitsio.dir

# All Build rule for target.
CMakeFiles/debug_cfitsio.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=70 "Built target debug_cfitsio"
.PHONY : CMakeFiles/debug_cfitsio.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug_cfitsio.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/debug_cfitsio.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/debug_cfitsio.dir/rule

# Convenience name for target.
debug_cfitsio: CMakeFiles/debug_cfitsio.dir/rule
.PHONY : debug_cfitsio

# codegen rule for target.
CMakeFiles/debug_cfitsio.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=70 "Finished codegen for target debug_cfitsio"
.PHONY : CMakeFiles/debug_cfitsio.dir/codegen

# clean rule for target.
CMakeFiles/debug_cfitsio.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_cfitsio.dir/build.make CMakeFiles/debug_cfitsio.dir/clean
.PHONY : CMakeFiles/debug_cfitsio.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug_qfits_detailed.dir

# All Build rule for target.
CMakeFiles/debug_qfits_detailed.dir/all: CMakeFiles/libkd.dir/all
CMakeFiles/debug_qfits_detailed.dir/all: CMakeFiles/anbase.dir/all
CMakeFiles/debug_qfits_detailed.dir/all: CMakeFiles/qfits.dir/all
CMakeFiles/debug_qfits_detailed.dir/all: CMakeFiles/anutils.dir/all
CMakeFiles/debug_qfits_detailed.dir/all: CMakeFiles/anfiles.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=73 "Built target debug_qfits_detailed"
.PHONY : CMakeFiles/debug_qfits_detailed.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug_qfits_detailed.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 59
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/debug_qfits_detailed.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/debug_qfits_detailed.dir/rule

# Convenience name for target.
debug_qfits_detailed: CMakeFiles/debug_qfits_detailed.dir/rule
.PHONY : debug_qfits_detailed

# codegen rule for target.
CMakeFiles/debug_qfits_detailed.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=73 "Finished codegen for target debug_qfits_detailed"
.PHONY : CMakeFiles/debug_qfits_detailed.dir/codegen

# clean rule for target.
CMakeFiles/debug_qfits_detailed.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_qfits_detailed.dir/build.make CMakeFiles/debug_qfits_detailed.dir/clean
.PHONY : CMakeFiles/debug_qfits_detailed.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/copy_dlls.dir

# All Build rule for target.
CMakeFiles/copy_dlls.dir/all: CMakeFiles/solve-field.dir/all
CMakeFiles/copy_dlls.dir/all: CMakeFiles/astrometry-engine.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_dlls.dir/build.make CMakeFiles/copy_dlls.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_dlls.dir/build.make CMakeFiles/copy_dlls.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=69 "Built target copy_dlls"
.PHONY : CMakeFiles/copy_dlls.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/copy_dlls.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 88
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/copy_dlls.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /E/github/astrometry.net_win/build/CMakeFiles 0
.PHONY : CMakeFiles/copy_dlls.dir/rule

# Convenience name for target.
copy_dlls: CMakeFiles/copy_dlls.dir/rule
.PHONY : copy_dlls

# codegen rule for target.
CMakeFiles/copy_dlls.dir/codegen: CMakeFiles/solve-field.dir/all
CMakeFiles/copy_dlls.dir/codegen: CMakeFiles/astrometry-engine.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_dlls.dir/build.make CMakeFiles/copy_dlls.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=69 "Finished codegen for target copy_dlls"
.PHONY : CMakeFiles/copy_dlls.dir/codegen

# clean rule for target.
CMakeFiles/copy_dlls.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_dlls.dir/build.make CMakeFiles/copy_dlls.dir/clean
.PHONY : CMakeFiles/copy_dlls.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

