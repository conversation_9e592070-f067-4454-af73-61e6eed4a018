CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj: \
 E:/github/astrometry.net_win/catalogs/brightstars.c \
 C:/msys64/mingw64/include/assert.h C:/msys64/mingw64/include/crtdefs.h \
 C:/msys64/mingw64/include/corecrt.h C:/msys64/mingw64/include/_mingw.h \
 C:/msys64/mingw64/include/_mingw_mac.h \
 C:/msys64/mingw64/include/_mingw_secapi.h \
 C:/msys64/mingw64/include/vadefs.h \
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 E:/github/astrometry.net_win/include/astrometry/brightstars.h \
 E:/github/astrometry.net_win/catalogs/brightstars-data.c
