
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/catalogs/2mass.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/brightstars.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/constellation-boundaries.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/constellations.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/hd.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/nomad.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/stellarium-constellations.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/tycho2.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/ucac3.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/ucac4.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj.d"
  "E:/github/astrometry.net_win/catalogs/usnob.c" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj" "gcc" "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
