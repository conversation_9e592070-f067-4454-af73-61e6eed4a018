file(REMOVE_RECURSE
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj.d"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj"
  "CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj.d"
  "lib/libcatalogs.a"
  "lib/libcatalogs.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/catalogs.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
