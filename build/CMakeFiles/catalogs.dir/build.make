# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/catalogs.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/catalogs.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/catalogs.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/catalogs.dir/flags.make

CMakeFiles/catalogs.dir/codegen:
.PHONY : CMakeFiles/catalogs.dir/codegen

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj: E:/github/astrometry.net_win/catalogs/brightstars.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj -c /E/github/astrometry.net_win/catalogs/brightstars.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/brightstars.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/brightstars.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj: E:/github/astrometry.net_win/catalogs/constellation-boundaries.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj -c /E/github/astrometry.net_win/catalogs/constellation-boundaries.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/constellation-boundaries.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/constellation-boundaries.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj: E:/github/astrometry.net_win/catalogs/constellations.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj -c /E/github/astrometry.net_win/catalogs/constellations.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/constellations.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/constellations.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj: E:/github/astrometry.net_win/catalogs/hd.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj -c /E/github/astrometry.net_win/catalogs/hd.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/hd.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/hd.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj: E:/github/astrometry.net_win/catalogs/nomad.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj -c /E/github/astrometry.net_win/catalogs/nomad.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/nomad.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/nomad.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj: E:/github/astrometry.net_win/catalogs/tycho2.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj -c /E/github/astrometry.net_win/catalogs/tycho2.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/tycho2.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/tycho2.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj: E:/github/astrometry.net_win/catalogs/ucac3.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj -c /E/github/astrometry.net_win/catalogs/ucac3.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/ucac3.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/ucac3.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj: E:/github/astrometry.net_win/catalogs/ucac4.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj -c /E/github/astrometry.net_win/catalogs/ucac4.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/ucac4.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/ucac4.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj: E:/github/astrometry.net_win/catalogs/usnob.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj -c /E/github/astrometry.net_win/catalogs/usnob.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/usnob.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/usnob.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj: E:/github/astrometry.net_win/catalogs/2mass.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj -c /E/github/astrometry.net_win/catalogs/2mass.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/2mass.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/2mass.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.s

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj: CMakeFiles/catalogs.dir/flags.make
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj: CMakeFiles/catalogs.dir/includes_C.rsp
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj: E:/github/astrometry.net_win/catalogs/stellarium-constellations.c
CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj: CMakeFiles/catalogs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj -MF CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj.d -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj -c /E/github/astrometry.net_win/catalogs/stellarium-constellations.c

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/catalogs/stellarium-constellations.c > CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.i

CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/catalogs/stellarium-constellations.c -o CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.s

# Object files for target catalogs
catalogs_OBJECTS = \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj" \
"CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj"

# External object files for target catalogs
catalogs_EXTERNAL_OBJECTS =

lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/brightstars.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellation-boundaries.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/constellations.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/hd.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/nomad.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/tycho2.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac3.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/ucac4.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/usnob.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/2mass.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/E_/github/astrometry.net_win/catalogs/stellarium-constellations.c.obj
lib/libcatalogs.a: CMakeFiles/catalogs.dir/build.make
lib/libcatalogs.a: CMakeFiles/catalogs.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking C static library lib/libcatalogs.a"
	$(CMAKE_COMMAND) -P CMakeFiles/catalogs.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/catalogs.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/catalogs.dir/build: lib/libcatalogs.a
.PHONY : CMakeFiles/catalogs.dir/build

CMakeFiles/catalogs.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/catalogs.dir/cmake_clean.cmake
.PHONY : CMakeFiles/catalogs.dir/clean

CMakeFiles/catalogs.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/catalogs.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/catalogs.dir/depend

