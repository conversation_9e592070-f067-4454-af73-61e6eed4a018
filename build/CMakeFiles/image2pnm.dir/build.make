# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/image2pnm.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/image2pnm.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/image2pnm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/image2pnm.dir/flags.make

CMakeFiles/image2pnm.dir/codegen:
.PHONY : CMakeFiles/image2pnm.dir/codegen

CMakeFiles/image2pnm.dir/image2pnm.c.obj: CMakeFiles/image2pnm.dir/flags.make
CMakeFiles/image2pnm.dir/image2pnm.c.obj: E:/github/astrometry.net_win/win/image2pnm.c
CMakeFiles/image2pnm.dir/image2pnm.c.obj: CMakeFiles/image2pnm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/image2pnm.dir/image2pnm.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/image2pnm.dir/image2pnm.c.obj -MF CMakeFiles/image2pnm.dir/image2pnm.c.obj.d -o CMakeFiles/image2pnm.dir/image2pnm.c.obj -c /E/github/astrometry.net_win/win/image2pnm.c

CMakeFiles/image2pnm.dir/image2pnm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/image2pnm.dir/image2pnm.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/image2pnm.c > CMakeFiles/image2pnm.dir/image2pnm.c.i

CMakeFiles/image2pnm.dir/image2pnm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/image2pnm.dir/image2pnm.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/image2pnm.c -o CMakeFiles/image2pnm.dir/image2pnm.c.s

# Object files for target image2pnm
image2pnm_OBJECTS = \
"CMakeFiles/image2pnm.dir/image2pnm.c.obj"

# External object files for target image2pnm
image2pnm_EXTERNAL_OBJECTS =

bin/image2pnm.exe: CMakeFiles/image2pnm.dir/image2pnm.c.obj
bin/image2pnm.exe: CMakeFiles/image2pnm.dir/build.make
bin/image2pnm.exe: C:/msys64/mingw64/lib/libregex.dll.a
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/image2pnm.exe"
	/C/msys64/mingw64/bin/cmake.exe -E rm -f CMakeFiles/image2pnm.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/image2pnm.dir/objects.a $(image2pnm_OBJECTS) $(image2pnm_EXTERNAL_OBJECTS)
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/image2pnm.dir/objects.a -Wl,--no-whole-archive -o bin/image2pnm.exe -Wl,--out-implib,lib/libimage2pnm.dll.a -Wl,--major-image-version,0,--minor-image-version,0  -lm -lws2_32 /C/msys64/mingw64/lib/libregex.dll.a -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

# Rule to build all files generated by this target.
CMakeFiles/image2pnm.dir/build: bin/image2pnm.exe
.PHONY : CMakeFiles/image2pnm.dir/build

CMakeFiles/image2pnm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/image2pnm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/image2pnm.dir/clean

CMakeFiles/image2pnm.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/image2pnm.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/image2pnm.dir/depend

