# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/libkd.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/libkd.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/libkd.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/libkd.dir/flags.make

CMakeFiles/libkd.dir/codegen:
.PHONY : CMakeFiles/libkd.dir/codegen

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj: E:/github/astrometry.net_win/libkd/kdtree.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj -c /E/github/astrometry.net_win/libkd/kdtree.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdtree.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdtree.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj: E:/github/astrometry.net_win/libkd/kdtree_dim.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj -c /E/github/astrometry.net_win/libkd/kdtree_dim.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdtree_dim.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdtree_dim.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj: E:/github/astrometry.net_win/libkd/kdtree_fits_io.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj -c /E/github/astrometry.net_win/libkd/kdtree_fits_io.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdtree_fits_io.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdtree_fits_io.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj: E:/github/astrometry.net_win/libkd/kdint_ddd.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj -c /E/github/astrometry.net_win/libkd/kdint_ddd.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_ddd.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_ddd.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj: E:/github/astrometry.net_win/libkd/kdint_dds.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj -c /E/github/astrometry.net_win/libkd/kdint_dds.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_dds.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_dds.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj: E:/github/astrometry.net_win/libkd/kdint_ddu.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj -c /E/github/astrometry.net_win/libkd/kdint_ddu.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_ddu.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_ddu.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj: E:/github/astrometry.net_win/libkd/kdint_dss.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj -c /E/github/astrometry.net_win/libkd/kdint_dss.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_dss.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_dss.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj: E:/github/astrometry.net_win/libkd/kdint_duu.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj -c /E/github/astrometry.net_win/libkd/kdint_duu.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_duu.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_duu.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj: E:/github/astrometry.net_win/libkd/kdint_fff.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj -c /E/github/astrometry.net_win/libkd/kdint_fff.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_fff.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_fff.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.s

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj: CMakeFiles/libkd.dir/flags.make
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj: E:/github/astrometry.net_win/libkd/kdint_lll.c
CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj: CMakeFiles/libkd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj -MF CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj.d -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj -c /E/github/astrometry.net_win/libkd/kdint_lll.c

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/libkd/kdint_lll.c > CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.i

CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/libkd/kdint_lll.c -o CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.s

# Object files for target libkd
libkd_OBJECTS = \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj" \
"CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj"

# External object files for target libkd
libkd_EXTERNAL_OBJECTS =

lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj
lib/liblibkd.a: CMakeFiles/libkd.dir/build.make
lib/liblibkd.a: CMakeFiles/libkd.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking C static library lib/liblibkd.a"
	$(CMAKE_COMMAND) -P CMakeFiles/libkd.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/libkd.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/libkd.dir/build: lib/liblibkd.a
.PHONY : CMakeFiles/libkd.dir/build

CMakeFiles/libkd.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/libkd.dir/cmake_clean.cmake
.PHONY : CMakeFiles/libkd.dir/clean

CMakeFiles/libkd.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/libkd.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/libkd.dir/depend

