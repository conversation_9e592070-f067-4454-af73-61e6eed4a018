file(REMOVE_RECURSE
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj.d"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj"
  "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj.d"
  "lib/liblibkd.a"
  "lib/liblibkd.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/libkd.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
