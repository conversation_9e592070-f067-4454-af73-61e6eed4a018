
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/libkd/kdint_ddd.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddd.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdint_dds.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dds.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdint_ddu.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_ddu.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdint_dss.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_dss.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdint_duu.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdint_fff.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_fff.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdint_lll.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_lll.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdtree.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdtree_dim.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_dim.c.obj.d"
  "E:/github/astrometry.net_win/libkd/kdtree_fits_io.c" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj" "gcc" "CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdtree_fits_io.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
