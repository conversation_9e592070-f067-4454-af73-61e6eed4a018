CMakeFiles/libkd.dir/E_/github/astrometry.net_win/libkd/kdint_duu.c.obj: \
 E:/github/astrometry.net_win/libkd/kdint_duu.c \
 E:/github/astrometry.net_win/include/astrometry/kdtree.h \
 C:/msys64/mingw64/include/stdio.h \
 C:/msys64/mingw64/include/corecrt_stdio_config.h \
 C:/msys64/mingw64/include/corecrt.h C:/msys64/mingw64/include/_mingw.h \
 C:/msys64/mingw64/include/_mingw_mac.h \
 C:/msys64/mingw64/include/_mingw_secapi.h \
 C:/msys64/mingw64/include/vadefs.h \
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 C:/msys64/mingw64/include/_mingw_off_t.h \
 C:/msys64/mingw64/include/swprintf.inl \
 C:/msys64/mingw64/include/sec_api/stdio_s.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdint.h \
 C:/msys64/mingw64/include/stdint.h C:/msys64/mingw64/include/crtdefs.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stddef.h \
 C:/msys64/mingw64/include/stddef.h \
 E:/github/astrometry.net_win/libkd/kdtree_internal_common.h \
 E:/github/astrometry.net_win/libkd/kdint_etype_d.h \
 E:/github/astrometry.net_win/libkd/kdint_dtype_u.h \
 E:/github/astrometry.net_win/libkd/kdint_ttype_u.h \
 E:/github/astrometry.net_win/libkd/kdtree_internal.c \
 C:/msys64/mingw64/include/assert.h C:/msys64/mingw64/include/stdlib.h \
 C:/msys64/mingw64/include/corecrt_wstdlib.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
 C:/msys64/mingw64/include/limits.h \
 C:/msys64/mingw64/include/sec_api/stdlib_s.h \
 C:/msys64/mingw64/include/malloc.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
 C:/msys64/mingw64/include/errno.h C:/msys64/mingw64/include/math.h \
 C:/msys64/mingw64/include/string.h \
 C:/msys64/mingw64/include/sec_api/string_s.h \
 E:/github/astrometry.net_win/include/astrometry/os-features.h \
 E:/github/astrometry.net_win/include/astrometry/os-features-config.h \
 C:/msys64/mingw64/include/sys/param.h \
 C:/msys64/mingw64/include/sys/types.h \
 E:/github/astrometry.net_win/libkd/kdtree_internal.h \
 E:/github/astrometry.net_win/include/astrometry/kdtree.h \
 E:/github/astrometry.net_win/libkd/kdtree_mem.h \
 E:/github/astrometry.net_win/include/astrometry/keywords.h \
 E:/github/astrometry.net_win/include/astrometry/errors.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
 C:/msys64/mingw64/include/stdarg.h \
 C:/msys64/mingw64/include/_mingw_stdarg.h \
 C:/msys64/mingw64/include/regex.h C:/msys64/mingw64/include/tre/tre.h \
 C:/msys64/mingw64/include/tre/tre-config.h \
 C:/msys64/mingw64/include/wchar.h \
 C:/msys64/mingw64/include/corecrt_wctype.h \
 C:/msys64/mingw64/include/_mingw_stat64.h \
 C:/msys64/mingw64/include/sec_api/wchar_s.h \
 E:/github/astrometry.net_win/include/astrometry/an-bool.h \
 E:/github/astrometry.net_win/include/astrometry/bl.h \
 E:/github/astrometry.net_win/include/astrometry/keywords.h \
 E:/github/astrometry.net_win/include/astrometry/bl-nl.h \
 E:/github/astrometry.net_win/include/astrometry/bl-nl.ph \
 E:/github/astrometry.net_win/include/astrometry/mathutil.h \
 E:/github/astrometry.net_win/include/astrometry/bl.h \
 E:/github/astrometry.net_win/libkd/kdtree_internal_dists.c \
 E:/github/astrometry.net_win/libkd/kdtree_internal_fits.c \
 E:/github/astrometry.net_win/include/astrometry/kdtree_fits_io.h \
 E:/github/astrometry.net_win/include/astrometry/fitsbin.h \
 E:/github/astrometry.net_win/include/astrometry/anqfits.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
 C:/msys64/mingw64/include/unistd.h C:/msys64/mingw64/include/io.h \
 C:/msys64/mingw64/include/process.h \
 C:/msys64/mingw64/include/corecrt_startup.h \
 C:/msys64/mingw64/include/getopt.h \
 C:/msys64/mingw64/include/pthread_unistd.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_table.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_keywords.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_image.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_time.h \
 E:/github/astrometry.net_win/include/astrometry/fitsioutils.h \
 E:/github/astrometry.net_win/include/astrometry/ioutils.h \
 C:/msys64/mingw64/include/time.h C:/msys64/mingw64/include/sys/timeb.h \
 C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
 C:/msys64/mingw64/include/_timeval.h \
 C:/msys64/mingw64/include/pthread_time.h \
 C:/msys64/mingw64/include/pthread_compat.h
