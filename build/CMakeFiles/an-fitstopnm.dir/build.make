# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/an-fitstopnm.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/an-fitstopnm.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/an-fitstopnm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/an-fitstopnm.dir/flags.make

CMakeFiles/an-fitstopnm.dir/codegen:
.PHONY : CMakeFiles/an-fitstopnm.dir/codegen

CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj: CMakeFiles/an-fitstopnm.dir/flags.make
CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj: CMakeFiles/an-fitstopnm.dir/includes_C.rsp
CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj: E:/github/astrometry.net_win/util/an-fitstopnm.c
CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj: CMakeFiles/an-fitstopnm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj -MF CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj.d -o CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj -c /E/github/astrometry.net_win/util/an-fitstopnm.c

CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/an-fitstopnm.c > CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.i

CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/an-fitstopnm.c -o CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.s

# Object files for target an-fitstopnm
an__fitstopnm_OBJECTS = \
"CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj"

# External object files for target an-fitstopnm
an__fitstopnm_EXTERNAL_OBJECTS =

bin/an-fitstopnm.exe: CMakeFiles/an-fitstopnm.dir/E_/github/astrometry.net_win/util/an-fitstopnm.c.obj
bin/an-fitstopnm.exe: CMakeFiles/an-fitstopnm.dir/build.make
bin/an-fitstopnm.exe: lib/libanutils.a
bin/an-fitstopnm.exe: lib/libanbase.a
bin/an-fitstopnm.exe: lib/libqfits.a
bin/an-fitstopnm.exe: C:/msys64/mingw64/lib/libregex.dll.a
bin/an-fitstopnm.exe: lib/libanbase.a
bin/an-fitstopnm.exe: CMakeFiles/an-fitstopnm.dir/linkLibs.rsp
bin/an-fitstopnm.exe: CMakeFiles/an-fitstopnm.dir/objects1.rsp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/an-fitstopnm.exe"
	/C/cmake/bin/cmake.exe -E rm -f CMakeFiles/an-fitstopnm.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/an-fitstopnm.dir/objects.a @CMakeFiles/an-fitstopnm.dir/objects1.rsp
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/an-fitstopnm.dir/objects.a -Wl,--no-whole-archive -o bin/an-fitstopnm.exe -Wl,--out-implib,lib/liban-fitstopnm.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles/an-fitstopnm.dir/linkLibs.rsp

# Rule to build all files generated by this target.
CMakeFiles/an-fitstopnm.dir/build: bin/an-fitstopnm.exe
.PHONY : CMakeFiles/an-fitstopnm.dir/build

CMakeFiles/an-fitstopnm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/an-fitstopnm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/an-fitstopnm.dir/clean

CMakeFiles/an-fitstopnm.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/an-fitstopnm.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/an-fitstopnm.dir/depend

