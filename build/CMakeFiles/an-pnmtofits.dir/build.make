# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/an-pnmtofits.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/an-pnmtofits.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/an-pnmtofits.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/an-pnmtofits.dir/flags.make

CMakeFiles/an-pnmtofits.dir/codegen:
.PHONY : CMakeFiles/an-pnmtofits.dir/codegen

CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj: CMakeFiles/an-pnmtofits.dir/flags.make
CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj: E:/github/astrometry.net_win/util/an-pnmtofits.c
CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj: CMakeFiles/an-pnmtofits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj -MF CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj.d -o CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj -c /E/github/astrometry.net_win/util/an-pnmtofits.c

CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/util/an-pnmtofits.c > CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.i

CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/util/an-pnmtofits.c -o CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.s

# Object files for target an-pnmtofits
an__pnmtofits_OBJECTS = \
"CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj"

# External object files for target an-pnmtofits
an__pnmtofits_EXTERNAL_OBJECTS =

bin/an-pnmtofits.exe: CMakeFiles/an-pnmtofits.dir/E_/github/astrometry.net_win/util/an-pnmtofits.c.obj
bin/an-pnmtofits.exe: CMakeFiles/an-pnmtofits.dir/build.make
bin/an-pnmtofits.exe: lib/libanutils.a
bin/an-pnmtofits.exe: lib/libanbase.a
bin/an-pnmtofits.exe: lib/libqfits.a
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/an-pnmtofits.exe"
	/C/msys64/mingw64/bin/cmake.exe -E rm -f CMakeFiles/an-pnmtofits.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/an-pnmtofits.dir/objects.a $(an__pnmtofits_OBJECTS) $(an__pnmtofits_EXTERNAL_OBJECTS)
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/an-pnmtofits.dir/objects.a -Wl,--no-whole-archive -o bin/an-pnmtofits.exe -Wl,--out-implib,lib/liban-pnmtofits.dll.a -Wl,--major-image-version,0,--minor-image-version,0  lib/libanutils.a lib/libanbase.a lib/libqfits.a -lcfitsio -lws2_32 -lregex -lm -lgsl -lgslcblas -lm -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

# Rule to build all files generated by this target.
CMakeFiles/an-pnmtofits.dir/build: bin/an-pnmtofits.exe
.PHONY : CMakeFiles/an-pnmtofits.dir/build

CMakeFiles/an-pnmtofits.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/an-pnmtofits.dir/cmake_clean.cmake
.PHONY : CMakeFiles/an-pnmtofits.dir/clean

CMakeFiles/an-pnmtofits.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/an-pnmtofits.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/an-pnmtofits.dir/depend

