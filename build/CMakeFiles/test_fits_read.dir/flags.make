# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# compile C with C:/msys64/mingw64/bin/gcc.exe
C_DEFINES = 

C_INCLUDES = -I/E/github/astrometry.net_win/win/../include/astrometry -I/E/github/astrometry.net_win/win/../include -I/E/github/astrometry.net_win/win/../util -I/E/github/astrometry.net_win/win/../solver -I/E/github/astrometry.net_win/win/../catalogs -I/E/github/astrometry.net_win/win/../libkd -I/E/github/astrometry.net_win/win/../qfits-an -I/E/github/astrometry.net_win/win/../gsl-an -I/E/github/astrometry.net_win/win

C_FLAGS = -g -O0 -Wall

