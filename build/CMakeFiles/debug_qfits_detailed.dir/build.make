# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/debug_qfits_detailed.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/debug_qfits_detailed.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/debug_qfits_detailed.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/debug_qfits_detailed.dir/flags.make

CMakeFiles/debug_qfits_detailed.dir/codegen:
.PHONY : CMakeFiles/debug_qfits_detailed.dir/codegen

CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj: CMakeFiles/debug_qfits_detailed.dir/flags.make
CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj: CMakeFiles/debug_qfits_detailed.dir/includes_C.rsp
CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj: E:/github/astrometry.net_win/win/debug_qfits_detailed.c
CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj: CMakeFiles/debug_qfits_detailed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj -MF CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj.d -o CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj -c /E/github/astrometry.net_win/win/debug_qfits_detailed.c

CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/debug_qfits_detailed.c > CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.i

CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/debug_qfits_detailed.c -o CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.s

# Object files for target debug_qfits_detailed
debug_qfits_detailed_OBJECTS = \
"CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj"

# External object files for target debug_qfits_detailed
debug_qfits_detailed_EXTERNAL_OBJECTS =

bin/debug_qfits_detailed.exe: CMakeFiles/debug_qfits_detailed.dir/debug_qfits_detailed.c.obj
bin/debug_qfits_detailed.exe: CMakeFiles/debug_qfits_detailed.dir/build.make
bin/debug_qfits_detailed.exe: lib/libanfiles.a
bin/debug_qfits_detailed.exe: lib/libanutils.a
bin/debug_qfits_detailed.exe: lib/libanbase.a
bin/debug_qfits_detailed.exe: lib/libqfits.a
bin/debug_qfits_detailed.exe: lib/liblibkd.a
bin/debug_qfits_detailed.exe: lib/libanbase.a
bin/debug_qfits_detailed.exe: CMakeFiles/debug_qfits_detailed.dir/linkLibs.rsp
bin/debug_qfits_detailed.exe: CMakeFiles/debug_qfits_detailed.dir/objects1.rsp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/debug_qfits_detailed.exe"
	/C/cmake/bin/cmake.exe -E rm -f CMakeFiles/debug_qfits_detailed.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/debug_qfits_detailed.dir/objects.a @CMakeFiles/debug_qfits_detailed.dir/objects1.rsp
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/debug_qfits_detailed.dir/objects.a -Wl,--no-whole-archive -o bin/debug_qfits_detailed.exe -Wl,--out-implib,lib/libdebug_qfits_detailed.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles/debug_qfits_detailed.dir/linkLibs.rsp

# Rule to build all files generated by this target.
CMakeFiles/debug_qfits_detailed.dir/build: bin/debug_qfits_detailed.exe
.PHONY : CMakeFiles/debug_qfits_detailed.dir/build

CMakeFiles/debug_qfits_detailed.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/debug_qfits_detailed.dir/cmake_clean.cmake
.PHONY : CMakeFiles/debug_qfits_detailed.dir/clean

CMakeFiles/debug_qfits_detailed.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/debug_qfits_detailed.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/debug_qfits_detailed.dir/depend

