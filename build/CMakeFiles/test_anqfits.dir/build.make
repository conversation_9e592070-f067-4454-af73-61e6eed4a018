# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/test_anqfits.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_anqfits.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_anqfits.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_anqfits.dir/flags.make

CMakeFiles/test_anqfits.dir/codegen:
.PHONY : CMakeFiles/test_anqfits.dir/codegen

CMakeFiles/test_anqfits.dir/test_anqfits.c.obj: CMakeFiles/test_anqfits.dir/flags.make
CMakeFiles/test_anqfits.dir/test_anqfits.c.obj: CMakeFiles/test_anqfits.dir/includes_C.rsp
CMakeFiles/test_anqfits.dir/test_anqfits.c.obj: E:/github/astrometry.net_win/win/test_anqfits.c
CMakeFiles/test_anqfits.dir/test_anqfits.c.obj: CMakeFiles/test_anqfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/test_anqfits.dir/test_anqfits.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_anqfits.dir/test_anqfits.c.obj -MF CMakeFiles/test_anqfits.dir/test_anqfits.c.obj.d -o CMakeFiles/test_anqfits.dir/test_anqfits.c.obj -c /E/github/astrometry.net_win/win/test_anqfits.c

CMakeFiles/test_anqfits.dir/test_anqfits.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_anqfits.dir/test_anqfits.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/test_anqfits.c > CMakeFiles/test_anqfits.dir/test_anqfits.c.i

CMakeFiles/test_anqfits.dir/test_anqfits.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_anqfits.dir/test_anqfits.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/test_anqfits.c -o CMakeFiles/test_anqfits.dir/test_anqfits.c.s

# Object files for target test_anqfits
test_anqfits_OBJECTS = \
"CMakeFiles/test_anqfits.dir/test_anqfits.c.obj"

# External object files for target test_anqfits
test_anqfits_EXTERNAL_OBJECTS =

bin/test_anqfits.exe: CMakeFiles/test_anqfits.dir/test_anqfits.c.obj
bin/test_anqfits.exe: CMakeFiles/test_anqfits.dir/build.make
bin/test_anqfits.exe: lib/libanbase.a
bin/test_anqfits.exe: lib/libqfits.a
bin/test_anqfits.exe: C:/msys64/mingw64/lib/libregex.dll.a
bin/test_anqfits.exe: lib/libanbase.a
bin/test_anqfits.exe: C:/msys64/mingw64/lib/libregex.dll.a
bin/test_anqfits.exe: CMakeFiles/test_anqfits.dir/linkLibs.rsp
bin/test_anqfits.exe: CMakeFiles/test_anqfits.dir/objects1.rsp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/test_anqfits.exe"
	/C/cmake/bin/cmake.exe -E rm -f CMakeFiles/test_anqfits.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/test_anqfits.dir/objects.a @CMakeFiles/test_anqfits.dir/objects1.rsp
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/test_anqfits.dir/objects.a -Wl,--no-whole-archive -o bin/test_anqfits.exe -Wl,--out-implib,lib/libtest_anqfits.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles/test_anqfits.dir/linkLibs.rsp

# Rule to build all files generated by this target.
CMakeFiles/test_anqfits.dir/build: bin/test_anqfits.exe
.PHONY : CMakeFiles/test_anqfits.dir/build

CMakeFiles/test_anqfits.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_anqfits.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_anqfits.dir/clean

CMakeFiles/test_anqfits.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/test_anqfits.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_anqfits.dir/depend

