# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MSYS Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeRCInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"
  "C:/cmake/share/cmake-3.31/Modules/FindPackageMessage.cmake"
  "C:/cmake/share/cmake-3.31/Modules/FindPkgConfig.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Linker/GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Linker/GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-windres.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake"
  "CMakeFiles/3.31.8/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeRCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeSystem.cmake"
  "E:/github/astrometry.net_win/win/CMakeLists.txt"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/qfits.dir/DependInfo.cmake"
  "CMakeFiles/anbase.dir/DependInfo.cmake"
  "CMakeFiles/anutils.dir/DependInfo.cmake"
  "CMakeFiles/anfiles.dir/DependInfo.cmake"
  "CMakeFiles/libkd.dir/DependInfo.cmake"
  "CMakeFiles/catalogs.dir/DependInfo.cmake"
  "CMakeFiles/astrometry.dir/DependInfo.cmake"
  "CMakeFiles/solve-field.dir/DependInfo.cmake"
  "CMakeFiles/astrometry-engine.dir/DependInfo.cmake"
  "CMakeFiles/test-find-executable.dir/DependInfo.cmake"
  "CMakeFiles/an-fitstopnm.dir/DependInfo.cmake"
  "CMakeFiles/an-pnmtofits.dir/DependInfo.cmake"
  "CMakeFiles/image2pnm.dir/DependInfo.cmake"
  "CMakeFiles/removelines.dir/DependInfo.cmake"
  "CMakeFiles/uniformize.dir/DependInfo.cmake"
  "CMakeFiles/test_fits_read.dir/DependInfo.cmake"
  "CMakeFiles/debug_fits.dir/DependInfo.cmake"
  "CMakeFiles/debug_cfitsio.dir/DependInfo.cmake"
  "CMakeFiles/debug_qfits_detailed.dir/DependInfo.cmake"
  "CMakeFiles/copy_dlls.dir/DependInfo.cmake"
  )
