# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/astrometry-engine.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/astrometry-engine.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/astrometry-engine.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/astrometry-engine.dir/flags.make

CMakeFiles/astrometry-engine.dir/codegen:
.PHONY : CMakeFiles/astrometry-engine.dir/codegen

CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj: CMakeFiles/astrometry-engine.dir/flags.make
CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj: E:/github/astrometry.net_win/solver/engine-main.c
CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj: CMakeFiles/astrometry-engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj -MF CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj.d -o CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj -c /E/github/astrometry.net_win/solver/engine-main.c

CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/solver/engine-main.c > CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.i

CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/solver/engine-main.c -o CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.s

# Object files for target astrometry-engine
astrometry__engine_OBJECTS = \
"CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj"

# External object files for target astrometry-engine
astrometry__engine_EXTERNAL_OBJECTS =

bin/astrometry-engine.exe: CMakeFiles/astrometry-engine.dir/E_/github/astrometry.net_win/solver/engine-main.c.obj
bin/astrometry-engine.exe: CMakeFiles/astrometry-engine.dir/build.make
bin/astrometry-engine.exe: lib/libastrometry.a
bin/astrometry-engine.exe: lib/libcatalogs.a
bin/astrometry-engine.exe: lib/libanutils.a
bin/astrometry-engine.exe: lib/libanfiles.a
bin/astrometry-engine.exe: lib/libanbase.a
bin/astrometry-engine.exe: lib/libqfits.a
bin/astrometry-engine.exe: lib/liblibkd.a
bin/astrometry-engine.exe: C:/msys64/mingw64/lib/libregex.dll.a
bin/astrometry-engine.exe: lib/libanutils.a
bin/astrometry-engine.exe: lib/libanbase.a
bin/astrometry-engine.exe: lib/libqfits.a
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/astrometry-engine.exe"
	/C/msys64/mingw64/bin/cmake.exe -E rm -f CMakeFiles/astrometry-engine.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/astrometry-engine.dir/objects.a $(astrometry__engine_OBJECTS) $(astrometry__engine_EXTERNAL_OBJECTS)
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/astrometry-engine.dir/objects.a -Wl,--no-whole-archive -o bin/astrometry-engine.exe -Wl,--out-implib,lib/libastrometry-engine.dll.a -Wl,--major-image-version,0,--minor-image-version,0  lib/libastrometry.a lib/libcatalogs.a lib/libanutils.a lib/libanfiles.a lib/libanbase.a lib/libqfits.a lib/liblibkd.a -lcfitsio -lm -lws2_32 /C/msys64/mingw64/lib/libregex.dll.a lib/libanutils.a lib/libanbase.a lib/libqfits.a -lgsl -lgslcblas -lm -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

# Rule to build all files generated by this target.
CMakeFiles/astrometry-engine.dir/build: bin/astrometry-engine.exe
.PHONY : CMakeFiles/astrometry-engine.dir/build

CMakeFiles/astrometry-engine.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/astrometry-engine.dir/cmake_clean.cmake
.PHONY : CMakeFiles/astrometry-engine.dir/clean

CMakeFiles/astrometry-engine.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/astrometry-engine.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/astrometry-engine.dir/depend

