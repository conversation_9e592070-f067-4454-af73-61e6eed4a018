CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj: \
 E:/github/astrometry.net_win/qfits-an/qfits_rw.c \
 C:/msys64/mingw64/include/stdio.h \
 C:/msys64/mingw64/include/corecrt_stdio_config.h \
 C:/msys64/mingw64/include/corecrt.h C:/msys64/mingw64/include/_mingw.h \
 C:/msys64/mingw64/include/_mingw_mac.h \
 C:/msys64/mingw64/include/_mingw_secapi.h \
 C:/msys64/mingw64/include/vadefs.h \
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 C:/msys64/mingw64/include/_mingw_off_t.h \
 C:/msys64/mingw64/include/swprintf.inl \
 C:/msys64/mingw64/include/sec_api/stdio_s.h \
 C:/msys64/mingw64/include/stdlib.h \
 C:/msys64/mingw64/include/corecrt_wstdlib.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
 C:/msys64/mingw64/include/limits.h C:/msys64/mingw64/include/crtdefs.h \
 C:/msys64/mingw64/include/sec_api/stdlib_s.h \
 C:/msys64/mingw64/include/malloc.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
 C:/msys64/mingw64/include/errno.h C:/msys64/mingw64/include/unistd.h \
 C:/msys64/mingw64/include/io.h C:/msys64/mingw64/include/string.h \
 C:/msys64/mingw64/include/sec_api/string_s.h \
 C:/msys64/mingw64/include/process.h \
 C:/msys64/mingw64/include/corecrt_startup.h \
 C:/msys64/mingw64/include/sys/types.h C:/msys64/mingw64/include/getopt.h \
 C:/msys64/mingw64/include/pthread_unistd.h \
 C:/msys64/mingw64/include/sys/stat.h \
 C:/msys64/mingw64/include/_mingw_stat64.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_rw.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_header.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_card.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
 C:/msys64/mingw64/include/sys/param.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_tools.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_std.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_error.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/stdarg.h \
 C:/msys64/mingw64/include/stdarg.h \
 C:/msys64/mingw64/include/_mingw_stdarg.h \
 E:/github/astrometry.net_win/include/astrometry/qfits_memory.h
