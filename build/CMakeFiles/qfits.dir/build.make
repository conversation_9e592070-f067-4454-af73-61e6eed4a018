# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/msys64/mingw64/bin/cmake.exe

# The command to remove a file.
RM = /C/msys64/mingw64/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/qfits.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/qfits.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/qfits.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/qfits.dir/flags.make

CMakeFiles/qfits.dir/codegen:
.PHONY : CMakeFiles/qfits.dir/codegen

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj: E:/github/astrometry.net_win/qfits-an/anqfits.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj -c /E/github/astrometry.net_win/qfits-an/anqfits.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/anqfits.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/anqfits.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_tools.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_tools.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_tools.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_tools.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_table.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_table.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_table.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_table.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_float.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_float.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_float.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_float.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_error.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_error.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_error.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_error.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_time.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_time.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_time.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_time.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_card.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_card.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_card.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_card.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_header.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_header.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_header.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_header.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_rw.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_rw.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_rw.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_rw.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_memory.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_memory.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_memory.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_memory.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_convert.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_convert.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_convert.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_convert.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_byteswap.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_byteswap.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_byteswap.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_byteswap.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_image.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_image.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_image.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_image.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj: E:/github/astrometry.net_win/qfits-an/qfits_md5.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj -c /E/github/astrometry.net_win/qfits-an/qfits_md5.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/qfits_md5.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/qfits_md5.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.s

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj: E:/github/astrometry.net_win/qfits-an/md5.c
CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj -MF CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj.d -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj -c /E/github/astrometry.net_win/qfits-an/md5.c

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/qfits-an/md5.c > CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.i

CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/qfits-an/md5.c -o CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.s

CMakeFiles/qfits.dir/mman.c.obj: CMakeFiles/qfits.dir/flags.make
CMakeFiles/qfits.dir/mman.c.obj: E:/github/astrometry.net_win/win/mman.c
CMakeFiles/qfits.dir/mman.c.obj: CMakeFiles/qfits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/qfits.dir/mman.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/qfits.dir/mman.c.obj -MF CMakeFiles/qfits.dir/mman.c.obj.d -o CMakeFiles/qfits.dir/mman.c.obj -c /E/github/astrometry.net_win/win/mman.c

CMakeFiles/qfits.dir/mman.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/qfits.dir/mman.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/mman.c > CMakeFiles/qfits.dir/mman.c.i

CMakeFiles/qfits.dir/mman.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/qfits.dir/mman.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/mman.c -o CMakeFiles/qfits.dir/mman.c.s

# Object files for target qfits
qfits_OBJECTS = \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj" \
"CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj" \
"CMakeFiles/qfits.dir/mman.c.obj"

# External object files for target qfits
qfits_EXTERNAL_OBJECTS =

lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/mman.c.obj
lib/libqfits.a: CMakeFiles/qfits.dir/build.make
lib/libqfits.a: CMakeFiles/qfits.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Linking C static library lib/libqfits.a"
	$(CMAKE_COMMAND) -P CMakeFiles/qfits.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/qfits.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/qfits.dir/build: lib/libqfits.a
.PHONY : CMakeFiles/qfits.dir/build

CMakeFiles/qfits.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/qfits.dir/cmake_clean.cmake
.PHONY : CMakeFiles/qfits.dir/clean

CMakeFiles/qfits.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/qfits.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/qfits.dir/depend

