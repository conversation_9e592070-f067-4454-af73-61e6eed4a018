
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/astrometry.net_win/qfits-an/anqfits.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/anqfits.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/md5.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/md5.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_byteswap.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_byteswap.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_card.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_card.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_convert.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_convert.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_error.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_error.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_float.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_float.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_header.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_header.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_image.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_image.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_md5.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_md5.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_memory.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_memory.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_rw.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_rw.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_table.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_table.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_time.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_time.c.obj.d"
  "E:/github/astrometry.net_win/qfits-an/qfits_tools.c" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj" "gcc" "CMakeFiles/qfits.dir/E_/github/astrometry.net_win/qfits-an/qfits_tools.c.obj.d"
  "E:/github/astrometry.net_win/win/mman.c" "CMakeFiles/qfits.dir/mman.c.obj" "gcc" "CMakeFiles/qfits.dir/mman.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
