# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/removelines.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/removelines.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/removelines.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/removelines.dir/flags.make

CMakeFiles/removelines.dir/codegen:
.PHONY : CMakeFiles/removelines.dir/codegen

CMakeFiles/removelines.dir/removelines.c.obj: CMakeFiles/removelines.dir/flags.make
CMakeFiles/removelines.dir/removelines.c.obj: CMakeFiles/removelines.dir/includes_C.rsp
CMakeFiles/removelines.dir/removelines.c.obj: E:/github/astrometry.net_win/win/removelines.c
CMakeFiles/removelines.dir/removelines.c.obj: CMakeFiles/removelines.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/removelines.dir/removelines.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/removelines.dir/removelines.c.obj -MF CMakeFiles/removelines.dir/removelines.c.obj.d -o CMakeFiles/removelines.dir/removelines.c.obj -c /E/github/astrometry.net_win/win/removelines.c

CMakeFiles/removelines.dir/removelines.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/removelines.dir/removelines.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/removelines.c > CMakeFiles/removelines.dir/removelines.c.i

CMakeFiles/removelines.dir/removelines.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/removelines.dir/removelines.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/removelines.c -o CMakeFiles/removelines.dir/removelines.c.s

# Object files for target removelines
removelines_OBJECTS = \
"CMakeFiles/removelines.dir/removelines.c.obj"

# External object files for target removelines
removelines_EXTERNAL_OBJECTS =

bin/removelines.exe: CMakeFiles/removelines.dir/removelines.c.obj
bin/removelines.exe: CMakeFiles/removelines.dir/build.make
bin/removelines.exe: lib/libanfiles.a
bin/removelines.exe: lib/libanutils.a
bin/removelines.exe: lib/libanbase.a
bin/removelines.exe: lib/libqfits.a
bin/removelines.exe: lib/liblibkd.a
bin/removelines.exe: lib/libanbase.a
bin/removelines.exe: CMakeFiles/removelines.dir/linkLibs.rsp
bin/removelines.exe: CMakeFiles/removelines.dir/objects1.rsp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/removelines.exe"
	/C/cmake/bin/cmake.exe -E rm -f CMakeFiles/removelines.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/removelines.dir/objects.a @CMakeFiles/removelines.dir/objects1.rsp
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/removelines.dir/objects.a -Wl,--no-whole-archive -o bin/removelines.exe -Wl,--out-implib,lib/libremovelines.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles/removelines.dir/linkLibs.rsp

# Rule to build all files generated by this target.
CMakeFiles/removelines.dir/build: bin/removelines.exe
.PHONY : CMakeFiles/removelines.dir/build

CMakeFiles/removelines.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/removelines.dir/cmake_clean.cmake
.PHONY : CMakeFiles/removelines.dir/clean

CMakeFiles/removelines.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/removelines.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/removelines.dir/depend

