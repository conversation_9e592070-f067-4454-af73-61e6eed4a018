# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /C/cmake/bin/cmake.exe

# The command to remove a file.
RM = /C/cmake/bin/cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /E/github/astrometry.net_win/win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /E/github/astrometry.net_win/build

# Include any dependencies generated for this target.
include CMakeFiles/test-find-executable.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test-find-executable.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test-find-executable.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test-find-executable.dir/flags.make

CMakeFiles/test-find-executable.dir/codegen:
.PHONY : CMakeFiles/test-find-executable.dir/codegen

CMakeFiles/test-find-executable.dir/test-find-executable.c.obj: CMakeFiles/test-find-executable.dir/flags.make
CMakeFiles/test-find-executable.dir/test-find-executable.c.obj: CMakeFiles/test-find-executable.dir/includes_C.rsp
CMakeFiles/test-find-executable.dir/test-find-executable.c.obj: E:/github/astrometry.net_win/win/test-find-executable.c
CMakeFiles/test-find-executable.dir/test-find-executable.c.obj: CMakeFiles/test-find-executable.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/test-find-executable.dir/test-find-executable.c.obj"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test-find-executable.dir/test-find-executable.c.obj -MF CMakeFiles/test-find-executable.dir/test-find-executable.c.obj.d -o CMakeFiles/test-find-executable.dir/test-find-executable.c.obj -c /E/github/astrometry.net_win/win/test-find-executable.c

CMakeFiles/test-find-executable.dir/test-find-executable.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test-find-executable.dir/test-find-executable.c.i"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /E/github/astrometry.net_win/win/test-find-executable.c > CMakeFiles/test-find-executable.dir/test-find-executable.c.i

CMakeFiles/test-find-executable.dir/test-find-executable.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test-find-executable.dir/test-find-executable.c.s"
	/C/msys64/mingw64/bin/gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /E/github/astrometry.net_win/win/test-find-executable.c -o CMakeFiles/test-find-executable.dir/test-find-executable.c.s

# Object files for target test-find-executable
test__find__executable_OBJECTS = \
"CMakeFiles/test-find-executable.dir/test-find-executable.c.obj"

# External object files for target test-find-executable
test__find__executable_EXTERNAL_OBJECTS =

bin/test-find-executable.exe: CMakeFiles/test-find-executable.dir/test-find-executable.c.obj
bin/test-find-executable.exe: CMakeFiles/test-find-executable.dir/build.make
bin/test-find-executable.exe: lib/libanutils.a
bin/test-find-executable.exe: lib/libanbase.a
bin/test-find-executable.exe: lib/libqfits.a
bin/test-find-executable.exe: C:/msys64/mingw64/lib/libregex.dll.a
bin/test-find-executable.exe: lib/libanbase.a
bin/test-find-executable.exe: CMakeFiles/test-find-executable.dir/linkLibs.rsp
bin/test-find-executable.exe: CMakeFiles/test-find-executable.dir/objects1.rsp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/E/github/astrometry.net_win/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable bin/test-find-executable.exe"
	/C/cmake/bin/cmake.exe -E rm -f CMakeFiles/test-find-executable.dir/objects.a
	/C/msys64/mingw64/bin/ar.exe qc CMakeFiles/test-find-executable.dir/objects.a @CMakeFiles/test-find-executable.dir/objects1.rsp
	/C/msys64/mingw64/bin/gcc.exe -g -O0 -Wall -Wl,--whole-archive CMakeFiles/test-find-executable.dir/objects.a -Wl,--no-whole-archive -o bin/test-find-executable.exe -Wl,--out-implib,lib/libtest-find-executable.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles/test-find-executable.dir/linkLibs.rsp

# Rule to build all files generated by this target.
CMakeFiles/test-find-executable.dir/build: bin/test-find-executable.exe
.PHONY : CMakeFiles/test-find-executable.dir/build

CMakeFiles/test-find-executable.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test-find-executable.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test-find-executable.dir/clean

CMakeFiles/test-find-executable.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /E/github/astrometry.net_win/win /E/github/astrometry.net_win/win /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build /E/github/astrometry.net_win/build/CMakeFiles/test-find-executable.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test-find-executable.dir/depend

