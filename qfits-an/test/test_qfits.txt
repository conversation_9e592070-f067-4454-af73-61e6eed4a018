qtest:		-----> Header creation
qtest:		Creating blank header
qtest:		Destroying blank header
qtest:		Creating minimal header
qtest:		Inserting primary keywords
qtest:		Inserting history keywords
qtest:		Inserting comment keywords
qtest:		Inserting hierarch keywords
qtest:		Inserting mandatory keywords
qtest:		Opening file for output
qtest:		Dumping header to file
qtest:		Destroying built header
qtest:		-----> Dumping pixels
qtest:		-----> Header reading test
qtest:		Reading header from file
qtest:		Querying mandatory keys
qtest:		Querying base keys
qtest:		Checking key types
qtest:		Querying hierarch keys
qtest:		Removing keys
qtest:		Modifying keys
qtest:		-----> Header browsing test
qtest:		Reading header from file
qtest:		-----> Data loading test
qtest:		Initializing loader
qtest:		Loading pixel buffer
qtest:		-----> File with multiple extensions
qtest:		Creating default header
qtest:		Dumping header to test file
qtest:		Creating first extension with float pixels
qtest:		Dumping ext header 1 to test file
qtest:		Dumping float array
qtest:		Creating second extension with int pixels
qtest:		Dumping ext header 2 to test file
qtest:		Dumping int array
qtest:		Creating third extension with double pixels
qtest:		Dumping ext header 3 to test file
qtest:		Dumping double array
qtest:		File DATAMD5 signature is Ok
