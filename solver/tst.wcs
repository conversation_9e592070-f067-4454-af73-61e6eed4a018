SIMPLE  =                    T / Standard FITS file                             BITPIX  =                    8 / ASCII or bytes array                           NAXIS   =                    0 / Minimal header                                 EXTEND  =                    T / There may be FITS ext                          CTYPE1  = 'RA---TAN' / TAN (gnomic) projection                                  CTYPE2  = 'DEC--TAN' / TAN (gnomic) projection                                  WCSAXES =                    2 / no comment                                     EQUINOX =               2000.0 / Equatorial coordinates definition (yr)         LONPOLE =                180.0 / no comment                                     LATPOLE =                  0.0 / no comment                                     CRVAL1  =                    0 / RA  of reference point                         CRVAL2  =                    0 / DEC of reference point                         CRPIX1  =                  400 / X reference pixel                              CRPIX2  =                  400 / Y reference pixel                              CUNIT1  = 'deg     ' / X pixel scale units                                      CUNIT2  = 'deg     ' / Y pixel scale units                                      CD1_1   =                0.005 / Transformation matrix                          CD1_2   =                    0 / no comment                                     CD2_1   =                    0 / no comment                                     CD2_2   =                0.005 / no comment                                     IMAGEW  =                  800 / Image width,  in pixels.                       IMAGEH  =                  800 / Image height, in pixels.                       HISTORY Created by the Astrometry.net suite.                                    HISTORY For more details, see http://astrometry.net .                           HISTORY Subversion URL                                                          HISTORY   svn+ssh://astrometry.net/svn/trunk/src/astrometry/util/               HISTORY Subversion revision 14257                                               HISTORY Subversion date 2010-02-27 20:03:55 -0500 (Sat, 27 Feb                  HISTORY   2010)                                                                 HISTORY This WCS header was created by the Astrometry.net                       DATE    = '2010-03-01T21:01:32' / Date this file was created.                   COMMENT -- solver parameters: --                                                COMMENT Index(0): /Users/<USER>/INDEXES/index-709.fits                            COMMENT Index(1): /Users/<USER>/INDEXES/index-710.fits                            COMMENT Index(2): /Users/<USER>/INDEXES/index-711.fits                            COMMENT Index(3): /Users/<USER>/INDEXES/index-712.fits                            COMMENT Index(4): /Users/<USER>/INDEXES/index-713.fits                            COMMENT Index(5): /Users/<USER>/INDEXES/index-714.fits                            COMMENT Index(6): /Users/<USER>/INDEXES/index-715.fits                            COMMENT Field name: ./image-0321415282.axy                                      COMMENT Field scale lower: 6.99482 arcsec/pixel                                 COMMENT Field scale upper: 7.57772 arcsec/pixel                                 COMMENT X col name: X                                                           COMMENT Y col name: Y                                                           COMMENT Start obj: 0                                                            COMMENT End obj: 0                                                              COMMENT Solved_in: (null)                                                       COMMENT Solved_out: (null)                                                      COMMENT Solvedserver: (null)                                                    COMMENT Parity: 1                                                               COMMENT Codetol: 0.01                                                           COMMENT Verify pixels: 1 pix                                                    COMMENT Maxquads: 0                                                             COMMENT Maxmatches: 0                                                           COMMENT Cpu limit: 600.000000 s                                                 COMMENT Time limit: 0 s                                                         COMMENT Total time limit: 0 s                                                   COMMENT Total CPU limit: 0.000000 s                                             COMMENT Tweak: no                                                               COMMENT --                                                                      COMMENT -- properties of the matching quad: --                                  COMMENT index id: 712                                                           COMMENT index healpix: -1                                                       COMMENT index hpnside: 0                                                        COMMENT log odds: 438.83                                                        COMMENT odds: 3.81395e+190                                                      COMMENT quadno: 61659                                                           COMMENT stars: 83841,83977,83811,83996                                          COMMENT field: 6,5,0,4                                                          COMMENT code error: 0.00359107                                                  COMMENT nmatch: 71                                                              COMMENT nconflict: 0                                                            COMMENT nfield: 310                                                             COMMENT nindex: 81                                                              COMMENT scale: 7.29452 arcsec/pix                                               COMMENT parity: 1                                                               COMMENT quads tried: 16                                                         COMMENT quads matched: 1                                                        COMMENT quads verified: 0                                                       COMMENT objs tried: 7                                                           COMMENT cpu time: 0.005807                                                      COMMENT --                                                                      END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             