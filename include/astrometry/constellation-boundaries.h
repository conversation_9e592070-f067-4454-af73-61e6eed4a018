/*
# This file is part of the Astrometry.net suite.
# Licensed under a 3-clause BSD style license - see LICENSE
 */
#ifndef CONSTELLATION_BOUNDARIES_H
#define CONSTELLATION_BOUNDARIES_H

/**
 Returns the "enum constellations" number of the constellation
 containing the given RA,Dec point, or -1 if none such is found.
 */
int constellation_containing(double ra, double dec);

enum constellations {
    CON_AND,
    CON_ANT,
    CON_APS,
    CON_AQR,
    CON_AQL,
    CON_ARA,
    CON_ARI,
    CON_AUR,
    CON_BOO,
    CON_CAE,
    CON_CAM,
    CON_CNC,
    CON_CVN,
    CON_CMA,
    CON_CMI,
    CON_CAP,
    CON_CAR,
    CON_CAS,
    CON_CEN,
    CON_CEP,
    CON_CET,
    CON_CHA,
    CON_CIR,
    CON_COL,
    CON_COM,
    CON_CRA,
    CON_CRB,
    CON_CRV,
    CON_CRT,
    CON_CRU,
    CON_CYG,
    CON_DEL,
    CON_DOR,
    CON_DRA,
    CON_EQU,
    CON_ERI,
    CON_FOR,
    CON_GEM,
    CON_GRU,
    CON_HER,
    CON_HOR,
    CON_HYA,
    CON_HYI,
    CON_IND,
    CON_LAC,
    CON_LEO,
    CON_LMI,
    CON_LEP,
    CON_LIB,
    CON_LUP,
    CON_LYN,
    CON_LYR,
    CON_MEN,
    CON_MIC,
    CON_MON,
    CON_MUS,
    CON_NOR,
    CON_OCT,
    CON_OPH,
    CON_ORI,
    CON_PAV,
    CON_PEG,
    CON_PER,
    CON_PHE,
    CON_PIC,
    CON_PSC,
    CON_PSA,
    CON_PUP,
    CON_PYX,
    CON_RET,
    CON_SGE,
    CON_SGR,
    CON_SCO,
    CON_SCL,
    CON_SCT,
    CON_SER1,
    CON_SER2,
    CON_SEX,
    CON_TAU,
    CON_TEL,
    CON_TRI,
    CON_TRA,
    CON_TUC,
    CON_UMA,
    CON_UMI,
    CON_VEL,
    CON_VIR,
    CON_VOL,
    CON_VUL,
    CON_FINAL,
};

#endif
