This file lists some of the code we've imported from elsewhere.

First, the obvious ones:

-gsl-an/
  GSL (the GNU Scientific Library) v1.11
  Licensed under the GPL v3+.

-qfits-an/
  QFITS library v6.2, licensed under the GPL v2+.  Heavily modified by us.

Now, some miscellaneous files:

-util/EXIF.py
  3-clause BSD license.
  https://github.com/ianare/exif-py

-util/ctmf.c
  (c) <PERSON>, licensed under the GPL v3+.

-util/bt.c
  AVL code was yoinked from GNU libavl which is under the GPL.

-util/cutest.{c,h} and util/make-tests.sh
  Imported from cutest-1.4.  <PERSON><PERSON> sent upstream 24-01-2008.
  http://cutest.sourceforge.net/
  zlib/libpng license.

-util/md5.{c,h}
  (c) <PERSON>, licensed under the GPL v2+.

-util/qsort_reentrant.{c,h}
  From FreeBSD.  Distributed under the BSD license, big surprise.

-sdss/sqlcl.py
  (c) <PERSON>as <PERSON>

-sdss/yanny.py
  (c) <PERSON>

-solver/an_mm_malloc.h
  (c) FSF, from GCC.  GPL v3+.  Imported for use by ctmf.c

-solver/anet.py
  (c) Josh Bloom, licensed under GPL v3.

-solver/{tablist,modhead,fitscopy,tabmerge,fitstomatlab,liststruc,listhead,
        imcopy,imarith,imstat}.c
  From the CFITSIO utilities web page.
  http://heasarc.gsfc.nasa.gov/docs/software/fitsio/cexamples.html
  "You may freely modify, reuse, and redistribute these programs as you wish."
  (fitstomatlab.c is just a modified version of tablist.c)

-solver/fitsverify.c
  From the "fitsverify" tool,
  http://heasarc.gsfc.nasa.gov/docs/software/ftools/fitsverify/

-solver/pnpoly.c
  (c) Wm. Randolph Franklin, MIT license
  From http://www.ecse.rpi.edu/Homepages/wrf/Research/Short_Notes/pnpoly.html

-libkd/an-fls.h
  From FreeBSD; BSD 3-clause.


Data files:

-catalogs/constellation-boundaries.c
 IAU Constellation boundaries, in J2000, from
 http://vizier.cfa.harvard.edu/viz-bin/VizieR-3?-source=VI/49/bound_20

-catalogs/NGC.csv
 (c) Mattia Verga, from OpenNGC
 https://github.com/mattiaverga/OpenNGC
 Licensed under CC-BY-SA 4.0
 https://creativecommons.org/licenses/by-sa/4.0/

-catalogs/brightstars.fits and catalogs/brightstars-data.c
 From merging Stellarium's skycultures/*/star_names.fab data files with Hipparcos catalog.
 Stellarium is copyright (C) 2004-2019 Fabien Chereau et al.,
 and licensed under GPL v2+.

