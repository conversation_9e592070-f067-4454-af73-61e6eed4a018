# Travis-CI.org build script

# use the new containerized infrastructure
sudo: false

#dist: trusty
dist: bionic

# language: c
# compiler:
#   - gcc
#   - clang
# env:
#   - PY=2
#   - PY=3

language: python
python:
  - "2.7"
  - "3.5"
  - "3.6"
  - "3.7"
  - "3.8"
env:
  - CC=gcc
  - CC=clang

# Apparently the wcslib version they have (4.8.3) doesn't use pkg-config
# And netpbm include files are right in /usr/include, not in /usr/include/netpbm

script:
    - which python
    - which pip
    - python --version
    - pip --version
    - export PYTHON=$(which python)
    - export PIP=$(which pip)
    - $PYTHON --version
    - which $PYTHON
    - which $PIP
    - $PIP --version
    - $PIP install numpy
    - $PIP install fitsio
    - $PYTHON -c "import numpy as np"
    # quick py-only tests:
    - export PYVER=$(python -c "from __future__ import print_function; import sys; print('%i.%i' % sys.version_info[:2])")
    - if [ $PYVER != "2.7" ]; then PYTHONPATH=${PYTHONPATH}:. python util/timingpool.py; fi
    - make
    - make py
    - make extra
    - make test
    - (cd util && ./test)
    - (cd libkd && ./test)
    - (cd solver && ./test)
    - (cd plot && ./test)
    - make install INSTALL_DIR=~/an PYTHON_SCRIPT="/usr/bin/env $PYTHON"
    - ls ~/an/lib/python
    - export PYTHONPATH=${PYTHONPATH}:~/an/lib/python
    - (cd /tmp && $PYTHON -c "import astrometry.libkd.spherematch")
    - export PATH=${PATH}:~/an/bin
    - build-astrometry-index -d 3 -o index-9918.fits -P 18 -S mag -B 0.1 -s 0 -r 1 -I 9918 -M -i demo/tycho2-mag6.fits
    - echo -e 'add_path .\ninparallel\nindex index-9918.fits' > 99.cfg
    - solve-field --config 99.cfg demo/apod4.jpg  --continue
    - tablist demo/apod4.match
    - listhead demo/apod4.wcs

addons:
  apt:
    packages:
    - libnetpbm10
    - libnetpbm10-dev
    - netpbm
    - wcslib-dev
    - libcfitsio-dev
    - swig
    - gsl-bin
    - libgsl-dev

