# This is a config file for the Astrometry.net 'astrometry-engine'
# program - it contains information about where indices are stored,
# and "site policy" items.

# Check the indices in parallel?
#
# -if the indices you are using take less than 2 GB of space, and you have at least
#  as much physical memory as indices, then you want this enabled.
#
# -if you are using a 64-bit machine and you have enough physical memory to contain
#  the indices you are using, then you want this enabled.
# 
# -otherwise, leave it commented-out.

inparallel

# If no scale estimate is given, use these limits on field width.
# minwidth 0.1
# maxwidth 180

# If no depths are given, use these:
#depths 10 20 30 40 50 60 70 80 90 100

# Maximum CPU time to spend on a field, in seconds:
# default is 600 (ten minutes), which is probably way overkill.
cpulimit 300

# In which directories should we search for indices?
add_path E:/astrometry.net.index/

# Load any indices found in the directories listed above.
autoindex

## Or... explicitly list the indices to load.
#index index-219
#index index-218
#index index-217
#index index-216
#index index-215
#index index-214
#index index-213
#index index-212
#index index-211
#index index-210
#index index-209
#index index-208
#index index-207
#index index-206
#index index-205
#index index-204-00
#index index-204-01
#index index-204-02
#index index-204-03
#index index-204-04
#index index-204-05
#index index-204-06
#index index-204-07
#index index-204-08
#index index-204-09
#index index-204-10
#index index-204-11
#index index-203-00
#index index-203-01
#index index-203-02
#index index-203-03
#index index-203-04
#index index-203-05
#index index-203-06
#index index-203-07
#index index-203-08
#index index-203-09
#index index-203-10
#index index-203-11
#index index-202-00
#index index-202-01
#index index-202-02
#index index-202-03
#index index-202-04
#index index-202-05
#index index-202-06
#index index-202-07
#index index-202-08
#index index-202-09
#index index-202-10
#index index-202-11
#index index-201-00
#index index-201-01
#index index-201-02
#index index-201-03
#index index-201-04
#index index-201-05
#index index-201-06
#index index-201-07
#index index-201-08
#index index-201-09
#index index-201-10
#index index-201-11
#index index-200-00
#index index-200-01
#index index-200-02
#index index-200-03
#index index-200-04
#index index-200-05
#index index-200-06
#index index-200-07
#index index-200-08
#index index-200-09
#index index-200-10
#index index-200-11

