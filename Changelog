0.94:
	Netpbm: now uses pkg-config, if available.  The code has been modified to include the "netpbm/" directory prefix, so if you set the NETPBM_INC include path manually, you will likely need to change that to drop a "netpbm" off the end.

	libjpeg also now uses pkg-config.

	The version string generated by the Makefile (and used by setup.py) has been changed to be PEP440 compatible when using a git checkout, eg "0.93.dev5" rather than "0.93-5-g272cc984".

0.93:
	Minor fixes to work with numpy 1.24.

0.92:
	Minor.  Change the release build so that "make py" should work without swig.  Fix some SYSTEM_GSL issues in the Makefiles.

0.91:
	Minor fixes to work with numpy 1.23.

0.90:
	Tag of convenience for LegacySurvey.org work.

0.89:
	Small fix for 32-bit platforms.

0.88:
	Don't use HUGE_VAL, to avoid issues on Mac OSX.

0.87:
	Tag of convenience for LegacySurvey.org work.

0.86:
	Fix to bright-star catalog; add libkd uint64 datatype.

0.85:
	One tiny bit of GPL v2-only code was replaced with a BSD equivalent.

	The primary branch on Github has been changed to 'main'.

	Minor: an-pnmtofits adds a CTYPE3='RGB' card for the Aladin image
	viewer.

0.84:
	Minor: change the python 'multiproc' module to handle >2GB pickles.

0.83:
	Minor: re-add -K flag to build-astrometry-index to keep RA,Dec columns.

0.82:
	Simplify os-features tests by always using our vendored qsort_r.

0.81:
	All 'sextractor' command-line args renamed to 'source-extractor'.

	The word "blind" (in the ableist sense in which we were using it)
	has almost entirely been removed.

0.69:
	Tiny bugfixes over 0.68

0.68:
	Add extra driver programs for python executables (image2pnm, etc), named X rather than X.py.  Some python3 compatibility fixes.

0.67:
	Minor; python calls for WCS parsing from fitsio headers.  Tag for legacysurvey.org Data Release 3.

0.66:
	Minor; bugfix to enable building on Fedora Rawhide

0.65:
	Minor; add wcs_pv2sip; fixes for Solaris.

0.64:
	Minor; add sip_scale.  Tag for legacysurvey.org Data Release 2.

0.63:
	Additional minor changes for Debian packaging.

0.62:
	More relicensing with permission from contributors; remove radix.c

0.61:
	Minor changes for Debian packaging.

0.60:
	Relicense most of the Astrometry.net code proper under a BSD-3
	license.  Whole package still under GPL-3+ thanks to GNU gsl.

0.57:
	Makefile fixes: create dirs before install; propagate build flags
	Add man pages from Ole Streicher
	Some py3 friendliness
	Add timingpool.py

0.56:
	implement anwcs_write_to
	Makefile files for install-indexes
	add -std=gnu89 to default CFLAGS
	add clip_wcs() method

0.55:
	Fix bug in WCS numpy array iterators

0.54:
	Try importing pyfits and then astropy.io.fits

0.53:
	Update wcs-pv2sip and include in default build
	Fixes for git version strings
	Read and write linear SIP distortion terms
	multiindex: don't assert when reloading already-loaded file

0.52:
	Fix release version strings in released versions.

0.51:
	Fix half-pixel bug in "solve-field --crpix-center": the center was
	set to 1 + {W,H}/2, not 0.5 + {W,H}/2.  (The WCS was still
	correct, the CRPIX was set to a half pixel in each dimension away
	from the center pixel.


