#! /bin/sh

prefix=@prefix@
exec_prefix=@exec_prefix@
includedir=@includedir@

usage()
{
    cat <<EOF
Usage: gsl-config [OPTION]

Known values for OPTION are:

  --prefix		show GSL installation prefix 
  --libs		print library linking information, with cblas
  --libs-without-cblas	print library linking information, without cblas
  --cflags		print pre-processor and compiler flags
  --help		display this help and exit
  --version		output version information

An external CBLAS library can be specified using the GSL_CBLAS_LIB
environment variable. The GSL CBLAS library is used by default.

EOF

    exit $1
}

if test $# -eq 0; then
    usage 1
fi

cflags=false
libs=false

while test $# -gt 0; do
    case "$1" in
    -*=*) optarg=`echo "$1" | sed 's/[-_a-zA-Z0-9]*=//'` ;;
    *) optarg= ;;
    esac

    case "$1" in
    --prefix=*)
	prefix=$optarg
	;;

    --prefix)
	echo $prefix
	;;

    --version)
	echo @VERSION@
	exit 0
	;;

    --help)
	usage 0
	;;

    --cflags)
       	echo @GSL_CFLAGS@ 
       	;;

    --libs)
        : ${GSL_CBLAS_LIB=-lgslcblas}
       	echo @GSL_LIBS@ $GSL_CBLAS_LIB -lm
       	;;

    --libs-without-cblas)
       	echo @GSL_LIBS@ -lm
       	;;
    *)
	usage
	exit 1
	;;
    esac
    shift
done

exit 0
